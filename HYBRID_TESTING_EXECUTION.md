# 🚀 Hybrid Testing Execution Guide

## 📋 **Step-by-Step Instructions**

### **Phase 1: Environment Setup (5 minutes)**

#### **1.1 Run Setup Script**
```powershell
# Open PowerShell as Administrator
cd c:\Users\<USER>\neupria

# Run the setup script
.\scripts\setup-hybrid-testing.ps1

# If you get execution policy error:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\scripts\setup-hybrid-testing.ps1
```

#### **1.2 Verify Services Started**
```powershell
# Check Docker containers
docker-compose -f docker-compose/local/docker-compose.yml ps

# Expected output:
# lobe-postgres    Up    5432/tcp
# lobe-minio       Up    9000/tcp, 9001/tcp  
# lobe-casdoor     Up    8000/tcp
# lobe-searxng     Up    8080/tcp
# lobe-chat        Up    3210/tcp
```

### **Phase 2: API Key Configuration (10 minutes)**

#### **2.1 Edit Environment File**
```powershell
# Open the environment file
notepad docker-compose/local/.env
```

#### **2.2 Configure Azure OpenAI (Primary)**
```env
# Replace these values with your actual Azure OpenAI credentials
AZURE_API_KEY=your_actual_azure_key_here
AZURE_ENDPOINT=https://your-resource.openai.azure.com
ENABLED_AZURE_OPENAI=1
```

**How to get Azure OpenAI credentials:**
1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to your Azure OpenAI resource
3. Go to "Keys and Endpoint"
4. Copy Key 1 and Endpoint URL

#### **2.3 Configure AWS Bedrock (Optional)**
```env
# Replace with your AWS credentials
AWS_ACCESS_KEY_ID=your_actual_aws_key_here
AWS_SECRET_ACCESS_KEY=your_actual_aws_secret_here
ENABLED_AWS_BEDROCK=1
```

#### **2.4 Configure GCP Vertex AI (Optional)**
```env
# Replace with your GCP credentials
GOOGLE_API_KEY=your_actual_google_key_here
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
ENABLED_GOOGLE=1
```

### **Phase 3: Application Setup (5 minutes)**

#### **3.1 Install Dependencies**
```powershell
# Install Node.js dependencies
npm install

# Or if using pnpm
pnpm install
```

#### **3.2 Start Development Server**
```powershell
# Start the Next.js development server
npm run dev

# Server will start on http://localhost:3010
```

#### **3.3 Verify Health Check**
```powershell
# Test the health endpoint
curl http://localhost:3210/api/health

# Or open in browser:
start http://localhost:3210/api/health
```

### **Phase 4: Run Hybrid Tests (10 minutes)**

#### **4.1 Basic AI Integration Tests**
```powershell
# Run all AI integration tests
npm run test:ai

# Expected output:
# ✓ Azure OpenAI Integration > should successfully call GPT-4 model
# ✓ AWS Bedrock Integration > should successfully call Claude 3 Sonnet  
# ✓ GCP Vertex AI Integration > should successfully call Gemini Pro
```

#### **4.2 Provider-Specific Tests**
```powershell
# Test only Azure OpenAI
npm run test:ai:azure

# Test only AWS Bedrock
npm run test:ai:bedrock

# Test only GCP Vertex AI
npm run test:ai:vertex
```

#### **4.3 Performance Tests**
```powershell
# Run performance and load tests
npm run test:performance

# Expected metrics:
# - Response time < 10 seconds
# - Concurrent requests handled
# - Rate limiting respected
```

### **Phase 5: Interactive Testing (15 minutes)**

#### **5.1 Open Application**
```powershell
# Open the application in browser
start http://localhost:3210
```

#### **5.2 Test Chat Interface**
1. **Create Account**: Sign up through Casdoor auth
2. **Select Model**: Choose from available AI models
3. **Send Test Messages**:
   - "Hello, please respond with your model name"
   - "What's the weather like?" (test general knowledge)
   - "Explain quantum computing" (test complex reasoning)

#### **5.3 Test Model Switching**
1. Switch between different models (GPT-4, Claude, Gemini)
2. Send the same prompt to each model
3. Compare responses and performance

#### **5.4 Test File Upload**
1. Upload a PDF or text file
2. Ask questions about the file content
3. Verify knowledge base integration

### **Phase 6: Monitoring and Debugging (Ongoing)**

#### **6.1 Enable Debug Logging**
```env
# Add to your .env file
DEBUG=lobe:ai:*
LOG_AI_REQUESTS=1
```

#### **6.2 Monitor API Usage**
```powershell
# Check Azure OpenAI usage
# Go to Azure Portal > Your OpenAI Resource > Monitoring

# Check AWS Bedrock usage  
# Go to AWS Console > Bedrock > Usage

# Check GCP Vertex AI usage
# Go to GCP Console > Vertex AI > Monitoring
```

#### **6.3 View Application Logs**
```powershell
# View Docker container logs
docker-compose -f docker-compose/local/docker-compose.yml logs -f lobe-chat

# View specific service logs
docker logs lobe-postgres
docker logs lobe-minio
```

## 🎯 **Testing Scenarios**

### **Scenario 1: Model Comparison**
```javascript
// Test the same prompt across different models
const prompt = "Explain artificial intelligence in simple terms";

// Test with GPT-4
const gpt4Response = await sendMessage('gpt-4', prompt);

// Test with Claude
const claudeResponse = await sendMessage('claude-3-sonnet', prompt);

// Test with Gemini
const geminiResponse = await sendMessage('gemini-pro', prompt);

// Compare responses
console.log('GPT-4:', gpt4Response);
console.log('Claude:', claudeResponse);
console.log('Gemini:', geminiResponse);
```

### **Scenario 2: Vision Model Testing**
```javascript
// Test image understanding
const imagePrompt = "Describe what you see in this image";
const imageData = "data:image/png;base64,iVBORw0KGgo...";

// Test with GPT-4 Vision
const visionResponse = await sendMessageWithImage('gpt-4-vision', imagePrompt, imageData);
console.log('Vision Response:', visionResponse);
```

### **Scenario 3: Performance Testing**
```javascript
// Test concurrent requests
const promises = [];
for (let i = 0; i < 5; i++) {
  promises.push(sendMessage('gpt-4', `Test message ${i}`));
}

const responses = await Promise.all(promises);
console.log('All responses received:', responses.length);
```

## 🔧 **Troubleshooting**

### **Common Issues**

| Issue | Solution |
|-------|----------|
| **Docker containers not starting** | Run `docker-compose down` then `docker-compose up -d` |
| **401 Unauthorized errors** | Check API keys are correct and not expired |
| **Timeout errors** | Increase `AI_REQUEST_TIMEOUT` in .env |
| **Rate limit errors** | Reduce `MAX_CONCURRENT_REQUESTS` |
| **Model not found** | Verify model names match your deployments |

### **Debug Commands**
```powershell
# Check service status
docker-compose ps

# Restart specific service
docker-compose restart lobe-chat

# View detailed logs
docker-compose logs --tail=100 lobe-chat

# Test database connection
docker exec -it lobe-postgres psql -U postgres -d lobechat -c "SELECT 1;"

# Test MinIO connection
curl http://localhost:9000/minio/health/live
```

## ✅ **Success Criteria**

Your hybrid testing setup is successful when:

- [ ] All Docker containers are running
- [ ] Health check endpoint returns 200 OK
- [ ] At least one AI provider is configured and working
- [ ] Basic chat functionality works in the UI
- [ ] AI integration tests pass
- [ ] You can switch between different AI models
- [ ] File upload and knowledge base features work

## 🎉 **Next Steps**

Once hybrid testing is working:

1. **Develop Features**: Build new AI-powered features locally
2. **Write Tests**: Add more comprehensive test coverage
3. **Performance Optimization**: Profile and optimize AI API calls
4. **Cloud Deployment**: Deploy to GCP when ready
5. **Production Testing**: Test with real user scenarios

---

**Happy Testing! 🚀**
