# Development override for faster local development
# Use this for live code reloading without rebuilding Docker images

version: '3.8'

services:
  lobe:
    # Override the build to use volume mounts for faster development
    volumes:
      # Mount source code for live reloading
      - ../../src:/app/src:ro
      - ../../public:/app/public:ro
      - ../../package.json:/app/package.json:ro
      - ../../next.config.mjs:/app/next.config.mjs:ro
      - ../../tailwind.config.ts:/app/tailwind.config.ts:ro
      - ../../tsconfig.json:/app/tsconfig.json:ro
      # Mount node_modules from host (if you want to use host dependencies)
      # - ../../node_modules:/app/node_modules:ro
    environment:
      # Enable development mode
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      # Enable hot reloading
      - WATCHPACK_POLLING=true
    # Override entrypoint for development
    entrypoint: >
      /bin/sh -c "
        echo '🚀 Starting LobeChat in Development Mode'
        echo '📁 Source code mounted for live reloading'
        cd /app
        npm run dev -- --hostname 0.0.0.0 --port 3210
      "
    # Add development-specific labels
    labels:
      - "dev.mode=true"
      - "dev.live-reload=enabled"
