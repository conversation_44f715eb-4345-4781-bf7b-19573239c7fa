{"compilerOptions": {"allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "target": "ESNext", "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/main/*"], "~common/*": ["src/common/*"]}}, "include": ["src/main/**/*", "src/preload/**/*", "electron-builder.js"]}