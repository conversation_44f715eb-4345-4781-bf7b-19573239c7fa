# Proxy, if you need it
# HTTP_PROXY=http://localhost:7890
# HTTPS_PROXY=http://localhost:7890


# =============================================================================
# AI MODEL INTEGRATION - HYBRID TESTING CONFIGURATION
# =============================================================================
# Replace the placeholder values with your actual API keys and endpoints

# Azure OpenAI Configuration (Primary Testing Provider)
# Get these from: https://portal.azure.com -> Your OpenAI Resource
AZURE_API_KEY=your_azure_openai_key_here
AZURE_API_VERSION=2024-02-15-preview
AZURE_ENDPOINT=https://your-resource.openai.azure.com
ENABLED_AZURE_OPENAI=1
AZURE_MODEL_LIST=gpt-4,gpt-4-turbo,gpt-35-turbo,gpt-4-vision-preview

# AWS Bedrock Configuration (Claude Models)
# Get these from: AWS Console -> IAM -> Users -> Security Credentials
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
ENABLED_AWS_BEDROCK=1
AWS_BEDROCK_MODEL_LIST=anthropic.claude-3-sonnet-********-v1:0,anthropic.claude-3-haiku-********-v1:0

# GCP Vertex AI Configuration (Gemini Models)
# Download service account JSON from: GCP Console -> IAM -> Service Accounts
GOOGLE_APPLICATION_CREDENTIALS=./gcp-service-account.json
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_CLOUD_REGION=us-central1
GOOGLE_API_KEY=your_vertex_ai_key_here
ENABLED_GOOGLE=1
GOOGLE_MODEL_LIST=gemini-pro,gemini-pro-vision,gemini-1.5-pro

# Anthropic Direct API (Optional)
# Get from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_key_here
ENABLED_ANTHROPIC=1
ANTHROPIC_MODEL_LIST=claude-3-5-sonnet-********,claude-3-opus-********

# OpenAI Direct API (Optional)
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_key_here
ENABLED_OPENAI=1
OPENAI_MODEL_LIST=gpt-4,gpt-4-turbo,gpt-3.5-turbo,dall-e-3

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Enable debug logging for AI model calls
DEBUG=lobe:ai:*
NODE_ENV=development

# Rate limiting for testing (to avoid hitting API limits)
AI_REQUEST_TIMEOUT=30000
MAX_CONCURRENT_REQUESTS=5

# Test-specific settings
ENABLE_MODEL_TESTING=1
LOG_AI_REQUESTS=1
MOCK_EXPENSIVE_CALLS=0

# Other environment variables, as needed. You can refer to the environment variables configuration for the client version, making sure not to have ACCESS_CODE.
# OPENAI_API_KEY=sk-xxxx
# OPENAI_PROXY_URL=https://api.openai.com/v1
# OPENAI_MODEL_LIST=...


# ===========================
# ====== Preset config ======
# ===========================
# if no special requirements, no need to change
LOBE_PORT=3210
CASDOOR_PORT=8000
MINIO_PORT=9000
APP_URL=http://localhost:3210
AUTH_URL=http://localhost:3210/api/auth

# Postgres related, which are the necessary environment variables for DB
LOBE_DB_NAME=lobechat
POSTGRES_PASSWORD=uWNZugjBqixf8dxC

AUTH_CASDOOR_ISSUER=http://localhost:8000
# Casdoor secret
AUTH_CASDOOR_ID=a387a4892ee19b1a2249
AUTH_CASDOOR_SECRET=dbf205949d704de81b0b5b3603174e23fbecc354

# MinIO S3 configuration
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=YOUR_MINIO_PASSWORD

# Configure the bucket information of MinIO
S3_PUBLIC_DOMAIN=http://localhost:9000
S3_ENDPOINT=http://localhost:9000
MINIO_LOBE_BUCKET=lobe

# Configure for casdoor
origin=http://localhost:8000