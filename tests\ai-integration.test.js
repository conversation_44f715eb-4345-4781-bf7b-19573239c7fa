/**
 * AI Model Integration Tests - Hybrid Testing
 * Tests real cloud AI APIs with local application
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { config } from 'dotenv';
import axios from 'axios';

// Load environment variables
config({ path: 'docker-compose/local/.env' });

const BASE_URL = process.env.APP_URL || 'http://localhost:3210';
const TEST_TIMEOUT = 30000; // 30 seconds for AI API calls

describe('AI Model Integration Tests', () => {
  let authToken;
  let testUserId;

  beforeAll(async () => {
    // Wait for services to be ready
    await waitForServices();
    
    // Create test user and get auth token
    const authResult = await createTestUser();
    authToken = authResult.token;
    testUserId = authResult.userId;
  });

  afterAll(async () => {
    // Cleanup test user
    if (testUserId) {
      await cleanupTestUser(testUserId);
    }
  });

  describe('Azure OpenAI Integration', () => {
    it('should successfully call GPT-4 model', async () => {
      if (!process.env.AZURE_API_KEY || process.env.AZURE_API_KEY === 'your_azure_openai_key_here') {
        console.log('⏭️  Skipping Azure OpenAI test - API key not configured');
        return;
      }

      const response = await sendChatMessage({
        model: 'gpt-4',
        provider: 'azure',
        message: 'Hello, this is a test message. Please respond with "Test successful".',
        authToken
      });

      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
      expect(response.data.message.toLowerCase()).toContain('test successful');
      expect(response.data.model).toBe('gpt-4');
      expect(response.data.provider).toBe('azure');
    }, TEST_TIMEOUT);

    it('should handle GPT-4 Vision with image input', async () => {
      if (!process.env.AZURE_API_KEY) {
        console.log('⏭️  Skipping Azure Vision test - API key not configured');
        return;
      }

      const response = await sendChatMessage({
        model: 'gpt-4-vision-preview',
        provider: 'azure',
        message: 'Describe this image',
        image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        authToken
      });

      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
    }, TEST_TIMEOUT);
  });

  describe('AWS Bedrock Integration', () => {
    it('should successfully call Claude 3 Sonnet', async () => {
      if (!process.env.AWS_ACCESS_KEY_ID || process.env.AWS_ACCESS_KEY_ID === 'your_aws_access_key_here') {
        console.log('⏭️  Skipping AWS Bedrock test - credentials not configured');
        return;
      }

      const response = await sendChatMessage({
        model: 'anthropic.claude-3-sonnet-20240229-v1:0',
        provider: 'bedrock',
        message: 'Hello Claude, please respond with "Bedrock test successful".',
        authToken
      });

      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
      expect(response.data.message.toLowerCase()).toContain('bedrock test successful');
    }, TEST_TIMEOUT);

    it('should handle Claude 3 Haiku for fast responses', async () => {
      if (!process.env.AWS_ACCESS_KEY_ID) {
        console.log('⏭️  Skipping AWS Haiku test - credentials not configured');
        return;
      }

      const startTime = Date.now();
      const response = await sendChatMessage({
        model: 'anthropic.claude-3-haiku-20240307-v1:0',
        provider: 'bedrock',
        message: 'Quick test',
        authToken
      });

      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
      expect(responseTime).toBeLessThan(10000); // Haiku should be fast
    }, TEST_TIMEOUT);
  });

  describe('GCP Vertex AI Integration', () => {
    it('should successfully call Gemini Pro', async () => {
      if (!process.env.GOOGLE_API_KEY || process.env.GOOGLE_API_KEY === 'your_vertex_ai_key_here') {
        console.log('⏭️  Skipping GCP Vertex AI test - API key not configured');
        return;
      }

      const response = await sendChatMessage({
        model: 'gemini-pro',
        provider: 'google',
        message: 'Hello Gemini, please respond with "Vertex AI test successful".',
        authToken
      });

      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
      expect(response.data.message.toLowerCase()).toContain('vertex ai test successful');
    }, TEST_TIMEOUT);

    it('should handle Gemini Pro Vision', async () => {
      if (!process.env.GOOGLE_API_KEY) {
        console.log('⏭️  Skipping GCP Vision test - API key not configured');
        return;
      }

      const response = await sendChatMessage({
        model: 'gemini-pro-vision',
        provider: 'google',
        message: 'What do you see in this image?',
        image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        authToken
      });

      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
    }, TEST_TIMEOUT);
  });

  describe('Model Switching and Comparison', () => {
    it('should handle switching between different models', async () => {
      const models = [
        { model: 'gpt-4', provider: 'azure' },
        { model: 'anthropic.claude-3-sonnet-20240229-v1:0', provider: 'bedrock' },
        { model: 'gemini-pro', provider: 'google' }
      ];

      const testMessage = 'Respond with just your model name and provider.';
      const responses = [];

      for (const modelConfig of models) {
        try {
          const response = await sendChatMessage({
            ...modelConfig,
            message: testMessage,
            authToken
          });
          
          if (response.status === 200) {
            responses.push({
              model: modelConfig.model,
              provider: modelConfig.provider,
              response: response.data.message
            });
          }
        } catch (error) {
          console.log(`⏭️  Skipping ${modelConfig.provider}/${modelConfig.model} - not configured`);
        }
      }

      expect(responses.length).toBeGreaterThan(0);
      console.log('✅ Model responses:', responses);
    }, TEST_TIMEOUT * 3);
  });

  describe('Error Handling and Rate Limiting', () => {
    it('should handle invalid API keys gracefully', async () => {
      const response = await sendChatMessage({
        model: 'gpt-4',
        provider: 'azure',
        message: 'Test message',
        authToken,
        forceInvalidKey: true
      });

      expect(response.status).toBe(400);
      expect(response.data.error).toBeDefined();
    });

    it('should respect rate limiting', async () => {
      const promises = [];
      for (let i = 0; i < 3; i++) {
        promises.push(sendChatMessage({
          model: 'gpt-4',
          provider: 'azure',
          message: `Concurrent test ${i}`,
          authToken
        }));
      }

      const responses = await Promise.allSettled(promises);
      const successful = responses.filter(r => r.status === 'fulfilled').length;
      
      expect(successful).toBeGreaterThan(0);
    });
  });
});

// Helper functions
async function waitForServices() {
  const maxRetries = 30;
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      await axios.get(`${BASE_URL}/api/health`);
      console.log('✅ Services are ready');
      return;
    } catch (error) {
      retries++;
      console.log(`⏳ Waiting for services... (${retries}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  throw new Error('Services failed to start within timeout');
}

async function createTestUser() {
  const response = await axios.post(`${BASE_URL}/api/auth/test-user`, {
    email: `test-${Date.now()}@example.com`,
    plan: 'premium' // For unlimited testing
  });
  
  return {
    token: response.data.token,
    userId: response.data.userId
  };
}

async function cleanupTestUser(userId) {
  try {
    await axios.delete(`${BASE_URL}/api/auth/test-user/${userId}`);
  } catch (error) {
    console.log('⚠️  Failed to cleanup test user:', error.message);
  }
}

async function sendChatMessage({ model, provider, message, image, authToken, forceInvalidKey = false }) {
  const payload = {
    model,
    provider,
    messages: [
      {
        role: 'user',
        content: message
      }
    ]
  };

  if (image) {
    payload.messages[0].content = [
      { type: 'text', text: message },
      { type: 'image_url', image_url: { url: image } }
    ];
  }

  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  if (forceInvalidKey) {
    headers['X-Force-Invalid-Key'] = 'true';
  }

  try {
    return await axios.post(`${BASE_URL}/api/chat/completions`, payload, { headers });
  } catch (error) {
    return error.response || { status: 500, data: { error: error.message } };
  }
}
