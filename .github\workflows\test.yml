name: Test CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: paradedb/paradedb:latest
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready --health-interval 10s --health-timeout 5s  --health-retries 5


        ports:
          - 5432:5432

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ secrets.BUN_VERSION }}

      - name: Install deps
        run: bun i

      - name: Lint
        run: bun run lint

      - name: Test Server Coverage
        run: bun run test-server:coverage
        env:
          DATABASE_TEST_URL: postgresql://postgres:postgres@localhost:5432/postgres
          DATABASE_DRIVER: node
          NEXT_PUBLIC_SERVICE_MODE: server
          KEY_VAULTS_SECRET: LA7n9k3JdEcbSgml2sxfw+4TV1AzaaFU5+R176aQz4s=
          S3_PUBLIC_DOMAIN: https://example.com
          APP_URL: https://home.com

      - name: Upload Server coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage/server/lcov.info
          flags: server

      - name: Test App Coverage
        run: bun run test-app:coverage

      - name: Upload App Coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage/app/lcov.info
          flags: app
