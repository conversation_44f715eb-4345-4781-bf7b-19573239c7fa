# 🧪 Hybrid Testing Guide - AI Model Integration

This guide walks you through setting up hybrid testing where you run Neupria locally but integrate with real cloud AI APIs.

## 🎯 What is Hybrid Testing?

**Hybrid Testing** = **Local Infrastructure** + **Cloud AI APIs**

```
┌─────────────────────────────────────┐    ┌─────────────────────────────────────┐
│           LOCAL ENVIRONMENT         │    │           CLOUD AI APIS             │
│                                     │    │                                     │
│  ┌─────────────────────────────────┐ │    │  ┌─────────────────────────────────┐ │
│  │        LobeChat App             │ │────┼──│        Azure OpenAI             │ │
│  │      localhost:3210             │ │    │  │         (GPT-4, etc.)           │ │
│  └─────────────────────────────────┘ │    │  └─────────────────────────────────┘ │
│                                     │    │                                     │
│  ┌─────────────────────────────────┐ │    │  ┌─────────────────────────────────┐ │
│  │      PostgreSQL + pgvector      │ │    │  │        AWS Bedrock              │ │
│  │        localhost:5432           │ │────┼──│      (Claude 3, etc.)           │ │
│  └─────────────────────────────────┘ │    │  └─────────────────────────────────┘ │
│                                     │    │                                     │
│  ┌─────────────────────────────────┐ │    │  ┌─────────────────────────────────┐ │
│  │         MinIO S3                │ │    │  │       GCP Vertex AI             │ │
│  │        localhost:9000           │ │────┼──│      (Gemini Pro, etc.)         │ │
│  └─────────────────────────────────┘ │    │  └─────────────────────────────────┘ │
└─────────────────────────────────────┘    └─────────────────────────────────────┘
```

## 🚀 Quick Start (5 minutes)

### **Step 1: Run Setup Script**
```powershell
# Windows PowerShell
.\scripts\setup-hybrid-testing.ps1

# Or for quick start without API validation
.\scripts\setup-hybrid-testing.ps1 -QuickStart
```

### **Step 2: Configure API Keys**
Edit `docker-compose/local/.env` and replace placeholder values:

```env
# Azure OpenAI (Recommended - Most Stable)
AZURE_API_KEY=your_actual_azure_key_here
AZURE_ENDPOINT=https://your-resource.openai.azure.com

# AWS Bedrock (For Claude Models)
AWS_ACCESS_KEY_ID=your_actual_aws_key_here
AWS_SECRET_ACCESS_KEY=your_actual_aws_secret_here

# GCP Vertex AI (For Gemini Models)
GOOGLE_API_KEY=your_actual_google_key_here
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
```

### **Step 3: Start Testing**
```bash
# Install dependencies
npm install

# Run AI integration tests
npm run test:ai

# Start development server
npm run dev

# Open in browser
start http://localhost:3210
```

## 🔑 API Key Setup Guide

### **Azure OpenAI** (Primary Recommendation)

1. **Create Azure OpenAI Resource**
   - Go to [Azure Portal](https://portal.azure.com)
   - Create new resource → AI + Machine Learning → Azure OpenAI
   - Choose region (East US, West Europe recommended)

2. **Get API Credentials**
   ```
   Resource → Keys and Endpoint
   - Key 1: Copy this as AZURE_API_KEY
   - Endpoint: Copy this as AZURE_ENDPOINT
   ```

3. **Deploy Models**
   ```
   Resource → Model deployments → Create new deployment
   - gpt-4 (deployment name: gpt-4)
   - gpt-35-turbo (deployment name: gpt-35-turbo)
   - gpt-4-vision-preview (deployment name: gpt-4-vision-preview)
   ```

### **AWS Bedrock** (For Claude Models)

1. **Enable Bedrock Access**
   - Go to [AWS Console](https://console.aws.amazon.com)
   - Navigate to Amazon Bedrock
   - Request access to Anthropic Claude models

2. **Create IAM User**
   ```
   IAM → Users → Create user
   - Attach policy: AmazonBedrockFullAccess
   - Create access key → Copy Access Key ID and Secret
   ```

3. **Available Models**
   ```
   - anthropic.claude-3-sonnet-********-v1:0
   - anthropic.claude-3-haiku-********-v1:0
   - anthropic.claude-3-opus-********-v1:0
   ```

### **GCP Vertex AI** (For Gemini Models)

1. **Create GCP Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create new project or select existing

2. **Enable APIs**
   ```bash
   gcloud services enable aiplatform.googleapis.com
   gcloud services enable ml.googleapis.com
   ```

3. **Create Service Account**
   ```
   IAM & Admin → Service Accounts → Create Service Account
   - Role: Vertex AI User
   - Download JSON key file
   - Save as: gcp-service-account.json
   ```

## 🧪 Testing Scenarios

### **Scenario 1: Basic Model Testing**
```javascript
// Test each AI provider individually
describe('Basic AI Model Tests', () => {
  it('Azure OpenAI GPT-4', async () => {
    const response = await chat('gpt-4', 'Hello world');
    expect(response).toContain('Hello');
  });
  
  it('AWS Bedrock Claude', async () => {
    const response = await chat('claude-3-sonnet', 'Hello world');
    expect(response).toContain('Hello');
  });
  
  it('GCP Gemini Pro', async () => {
    const response = await chat('gemini-pro', 'Hello world');
    expect(response).toContain('Hello');
  });
});
```

### **Scenario 2: Model Comparison Testing**
```javascript
// Compare responses from different models
describe('Model Comparison', () => {
  it('should get different responses from different models', async () => {
    const prompt = 'Explain quantum computing in one sentence';
    
    const gpt4Response = await chat('gpt-4', prompt);
    const claudeResponse = await chat('claude-3-sonnet', prompt);
    const geminiResponse = await chat('gemini-pro', prompt);
    
    // Responses should be different but all valid
    expect(gpt4Response).not.toBe(claudeResponse);
    expect(claudeResponse).not.toBe(geminiResponse);
    expect(gpt4Response.length).toBeGreaterThan(10);
  });
});
```

### **Scenario 3: Vision Model Testing**
```javascript
// Test image understanding capabilities
describe('Vision Models', () => {
  it('should analyze images correctly', async () => {
    const imageBase64 = 'data:image/png;base64,...';
    
    const response = await chatWithImage('gpt-4-vision', 'What is in this image?', imageBase64);
    expect(response).toBeDefined();
    expect(response.length).toBeGreaterThan(10);
  });
});
```

### **Scenario 4: Performance Testing**
```javascript
// Test response times and concurrent requests
describe('Performance Tests', () => {
  it('should handle concurrent requests', async () => {
    const promises = Array(5).fill().map((_, i) => 
      chat('gpt-4', `Test message ${i}`)
    );
    
    const responses = await Promise.all(promises);
    expect(responses).toHaveLength(5);
    responses.forEach(response => {
      expect(response).toBeDefined();
    });
  });
});
```

## 📊 Testing Commands

```bash
# Run all AI integration tests
npm run test:ai

# Run specific provider tests
npm run test:ai -- --grep "Azure"
npm run test:ai -- --grep "Bedrock"
npm run test:ai -- --grep "Vertex"

# Run with verbose logging
DEBUG=lobe:ai:* npm run test:ai

# Run performance tests
npm run test:performance

# Run visual tests (with screenshots)
npm run test:visual
```

## 🔍 Debugging and Monitoring

### **Enable Debug Logging**
```env
# In your .env file
DEBUG=lobe:ai:*
LOG_AI_REQUESTS=1
```

### **Monitor API Usage**
```javascript
// Check API usage and costs
const usage = await getAPIUsage();
console.log('Azure OpenAI tokens used:', usage.azure.tokens);
console.log('AWS Bedrock requests:', usage.aws.requests);
console.log('GCP Vertex AI calls:', usage.gcp.calls);
```

### **Common Issues and Solutions**

| Issue | Solution |
|-------|----------|
| **401 Unauthorized** | Check API keys are correct and not expired |
| **403 Forbidden** | Verify model access permissions in cloud console |
| **429 Rate Limited** | Reduce concurrent requests or upgrade API plan |
| **Timeout Errors** | Increase `AI_REQUEST_TIMEOUT` in .env |
| **Model Not Found** | Check model deployment names match exactly |

## 💰 Cost Management

### **Estimated Costs for Testing**
```
Azure OpenAI GPT-4:     ~$0.03 per 1K tokens
AWS Bedrock Claude:     ~$0.015 per 1K tokens  
GCP Vertex AI Gemini:   ~$0.001 per 1K tokens
```

### **Cost Optimization Tips**
1. **Use cheaper models for basic tests** (GPT-3.5, Claude Haiku, Gemini Pro)
2. **Set request timeouts** to avoid hanging requests
3. **Limit concurrent requests** to avoid rate limit charges
4. **Use mock responses** for repetitive tests
5. **Monitor usage** with cloud provider dashboards

## 🎯 Next Steps

1. **Run the setup script**: `.\scripts\setup-hybrid-testing.ps1`
2. **Configure your API keys** in `.env` file
3. **Run basic tests**: `npm run test:ai`
4. **Start development**: `npm run dev`
5. **Build your features** with confidence in AI integration

## 📚 Additional Resources

- [Azure OpenAI Documentation](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/)
- [AWS Bedrock Documentation](https://docs.aws.amazon.com/bedrock/)
- [GCP Vertex AI Documentation](https://cloud.google.com/vertex-ai/docs)
- [LobeChat AI Integration Guide](./AI_INTEGRATION.md)

---

**Happy Testing! 🚀**
