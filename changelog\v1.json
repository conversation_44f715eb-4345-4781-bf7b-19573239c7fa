[{"children": {"fixes": ["Fix message refresh 401 on desktop."], "improvements": ["Add gemini & hunyuan & Claude models."]}, "date": "2025-05-25", "version": "1.88.3"}, {"children": {"improvements": ["Add live search support for xAI."]}, "date": "2025-05-24", "version": "1.88.2"}, {"children": {"fixes": ["User nickName & username selector in desktop."], "improvements": ["Support Gemini 2.5 thought reasoning."]}, "date": "2025-05-24", "version": "1.88.1"}, {"children": {"features": ["Add claude 4 series."], "fixes": ["Fix missing email field to user, update agent config of client db will override old config."]}, "date": "2025-05-23", "version": "1.88.0"}, {"children": {"improvements": ["Resolve InputNumber display overlap issue."]}, "date": "2025-05-23", "version": "1.87.9"}, {"children": {"fixes": ["'top_p' is not supported with o4-mini, pin zustand version to avoid type error."]}, "date": "2025-05-22", "version": "1.87.8"}, {"children": {"fixes": ["Bump @lobehub/ui to 2.1.7."]}, "date": "2025-05-21", "version": "1.87.7"}, {"children": {"improvements": ["Improve tools display."]}, "date": "2025-05-21", "version": "1.87.6"}, {"children": {"fixes": ["Fix desktop open issue on linux like Fedora42."]}, "date": "2025-05-20", "version": "1.87.5"}, {"children": {"fixes": ["Supported SenseNova v6 models correctly & update Gemini models."]}, "date": "2025-05-18", "version": "1.87.4"}, {"children": {"improvements": ["Clean code with new antd api."]}, "date": "2025-05-17", "version": "1.87.3"}, {"children": {"improvements": ["Support Doubao 1.5 Thinking Vision Pro model."]}, "date": "2025-05-16", "version": "1.87.2"}, {"children": {"improvements": ["Update internlm model list, add series, update Spark X1 model list & fix build-in search params."]}, "date": "2025-05-16", "version": "1.87.1"}, {"children": {"features": ["Support custom language and Mermaid Appearance."], "fixes": ["Fix oidc redirect url."]}, "date": "2025-05-16", "version": "1.87.0"}, {"children": {"improvements": ["Refactor agent runtime to model runtime."]}, "date": "2025-05-15", "version": "1.86.1"}, {"children": {"features": ["<PERSON>d <PERSON>."]}, "date": "2025-05-15", "version": "1.86.0"}, {"children": {"improvements": ["Update electron style on windows."]}, "date": "2025-05-14", "version": "1.85.10"}, {"children": {"fixes": ["Redirect unauthorized next-auth user to signin page."], "improvements": ["Improve smoothing on completion."]}, "date": "2025-05-14", "version": "1.85.9"}, {"children": {"fixes": ["Fix config import issue in the desktop version."]}, "date": "2025-05-11", "version": "1.85.8"}, {"children": {"fixes": ["Fix streamable http url valid and refactor local files to local system."]}, "date": "2025-05-11", "version": "1.85.7"}, {"children": {}, "date": "2025-05-10", "version": "1.85.6"}, {"children": {"fixes": ["Fix window close issue and release Window/Linux beta."]}, "date": "2025-05-10", "version": "1.85.5"}, {"children": {"fixes": ["Fix nothing return when reset the client db."]}, "date": "2025-05-10", "version": "1.85.4"}, {"children": {"fixes": ["Remove mcp client cache."], "improvements": ["Improve pdf and xlsx file content parser."]}, "date": "2025-05-10", "version": "1.85.3"}, {"children": {"improvements": ["Upgrade anthropic sdk."]}, "date": "2025-05-10", "version": "1.85.2"}, {"children": {"improvements": ["Add Qwen3 models for infiniai."]}, "date": "2025-05-10", "version": "1.85.1"}, {"children": {"features": ["Support upload files direct into chat context."]}, "date": "2025-05-09", "version": "1.85.0"}, {"children": {"improvements": ["Add reasoning tokens and token usage statistics for Google Gemini."]}, "date": "2025-05-09", "version": "1.84.27"}, {"children": {"improvements": ["Add qwen3 for ollama."]}, "date": "2025-05-08", "version": "1.84.26"}, {"children": {"fixes": ["Fix desktop upload image on macOS."]}, "date": "2025-05-08", "version": "1.84.25"}, {"children": {"fixes": ["Fix changelog issue on desktop app."]}, "date": "2025-05-08", "version": "1.84.24"}, {"children": {"improvements": ["Add new gemini & Mistral models."]}, "date": "2025-05-08", "version": "1.84.23"}, {"children": {"fixes": ["Fix electron state init on window."], "improvements": ["Add Xiaohongshu crawler rules."]}, "date": "2025-05-07", "version": "1.84.22"}, {"children": {"improvements": ["Remove doubao Provider."]}, "date": "2025-05-04", "version": "1.84.21"}, {"children": {"improvements": ["Show Aliyun Bailian tokens usage tracking."]}, "date": "2025-05-04", "version": "1.84.20"}, {"children": {"improvements": ["Fix init state of loading."]}, "date": "2025-05-04", "version": "1.84.19"}, {"children": {"improvements": ["Add perf stat support for openai factory."]}, "date": "2025-05-03", "version": "1.84.18"}, {"children": {"improvements": ["Add write file tool to local-file plugin."]}, "date": "2025-05-03", "version": "1.84.17"}, {"children": {"fixes": ["Fix desktop quiting with reopen window."]}, "date": "2025-05-02", "version": "1.84.16"}, {"children": {"fixes": ["Siliconflow requests with tools no longer force non-streaming."]}, "date": "2025-05-01", "version": "1.84.15"}, {"children": {"improvements": ["Add windows control and tray."]}, "date": "2025-05-01", "version": "1.84.14"}, {"children": {"improvements": ["Fix style issues."]}, "date": "2025-05-01", "version": "1.84.13"}, {"children": {"improvements": ["Support Qwen3 thinking_budget param."]}, "date": "2025-04-30", "version": "1.84.12"}, {"children": {"improvements": ["Allow copy/edit when generate error."]}, "date": "2025-04-30", "version": "1.84.11"}, {"children": {"improvements": ["Add Gemini 2.5 Pro Experimental model, Add siliconflow Qwen3 & support thinking params, Arrange model tags neatly, Update mobile style and fix issues problem, update ollama checker."]}, "date": "2025-04-30", "version": "1.84.10"}, {"children": {"fixes": ["Embeddings Format Issue with OpenAI API when using Ollama."]}, "date": "2025-04-30", "version": "1.84.9"}, {"children": {"fixes": ["Fix stdio mcp server env issue."]}, "date": "2025-04-29", "version": "1.84.8"}, {"children": {"fixes": ["Fix custom provider and knowledge base crash."]}, "date": "2025-04-29", "version": "1.84.7"}, {"children": {"improvements": ["Fix OpenRouter models config, improve mcp stdio custom field."]}, "date": "2025-04-29", "version": "1.84.6"}, {"children": {"improvements": ["Add Qwen3 models."]}, "date": "2025-04-29", "version": "1.84.5"}, {"children": {"improvements": ["Improve stdio mcp server install experience."]}, "date": "2025-04-28", "version": "1.84.4"}, {"children": {"fixes": ["Fix mcp server stdio spawn ENOENT in electron bundle, Fix mobile agent settings and textarea scroll, Remove gpt-4o-search-preview unsupported parameters."]}, "date": "2025-04-28", "version": "1.84.3"}, {"children": {}, "date": "2025-04-28", "version": "1.84.2"}, {"children": {"improvements": ["Improve hotkey for delete messages."]}, "date": "2025-04-28", "version": "1.84.1"}, {"children": {"features": ["Upgrade to lobe ui v2 with better design style."]}, "date": "2025-04-28", "version": "1.84.0"}, {"children": {"fixes": ["Fix cloud data sync."]}, "date": "2025-04-28", "version": "1.83.8"}, {"children": {"improvements": ["Fix doubao-1-5-thinking-pro-vision endpoint calling."]}, "date": "2025-04-27", "version": "1.83.7"}, {"children": {"fixes": ["Fix desktop cloud sync issue."]}, "date": "2025-04-27", "version": "1.83.6"}, {"children": {"fixes": ["Fix desktop beta redirect uris."]}, "date": "2025-04-27", "version": "1.83.5"}, {"children": {"fixes": ["Fix cloud connection issue."]}, "date": "2025-04-27", "version": "1.83.4"}, {"children": {}, "date": "2025-04-27", "version": "1.83.3"}, {"children": {}, "date": "2025-04-27", "version": "1.83.2"}, {"children": {}, "date": "2025-04-27", "version": "1.83.1"}, {"children": {"features": ["Support desktop release framework and workflow."]}, "date": "2025-04-27", "version": "1.83.0"}, {"children": {"improvements": ["Support ernie-x1 & ernie-4.5 series from Wenxin."]}, "date": "2025-04-26", "version": "1.82.10"}, {"children": {"improvements": ["Improve oidc OAuth workflow."]}, "date": "2025-04-26", "version": "1.82.9"}, {"children": {"improvements": ["Improve categories selection via SearXNG."], "fixes": ["Fix oidc redirect urls."]}, "date": "2025-04-26", "version": "1.82.8"}, {"children": {"fixes": ["Pwa-install cause mobile infinity scroll."]}, "date": "2025-04-25", "version": "1.82.7"}, {"children": {"fixes": ["Improve stability of Cloudflare Workers AI."]}, "date": "2025-04-24", "version": "1.82.6"}, {"children": {"fixes": ["Countries-and-timezones return invalid timezone."], "improvements": ["Add avatar for server database upload to S3, removing SSO dependency for avatar management."]}, "date": "2025-04-24", "version": "1.82.5"}, {"children": {"fixes": ["Fix hydration error."]}, "date": "2025-04-24", "version": "1.82.4"}, {"children": {"fixes": ["Fix openai tools calling."]}, "date": "2025-04-24", "version": "1.82.3"}, {"children": {"improvements": ["Improve stdio mcp form, Update the id of some OpenAI models in OpenRouter."]}, "date": "2025-04-23", "version": "1.82.2"}, {"children": {"improvements": ["Improve mcp server type select and fix refresh mcp manifest issue."]}, "date": "2025-04-23", "version": "1.82.1"}, {"children": {"features": ["Support Streamable HTTP MCP server."]}, "date": "2025-04-22", "version": "1.82.0"}, {"children": {"fixes": ["Fix search prompt."]}, "date": "2025-04-21", "version": "1.81.9"}, {"children": {"improvements": ["Show token generate performance."]}, "date": "2025-04-21", "version": "1.81.8"}, {"children": {"fixes": ["Together.ai fetch model list."]}, "date": "2025-04-21", "version": "1.81.7"}, {"children": {"improvements": ["Add hotkey for clear chat messages."]}, "date": "2025-04-21", "version": "1.81.6"}, {"children": {"fixes": ["Lock nextauth to beta.25."]}, "date": "2025-04-20", "version": "1.81.5"}, {"children": {"fixes": ["Gemini error Tool use with function calling is unsupported."], "improvements": ["Support update tool calling params and trigger again."]}, "date": "2025-04-19", "version": "1.81.4"}, {"children": {"improvements": ["Update GitHub & Cloudflare models, Update Google models."]}, "date": "2025-04-19", "version": "1.81.3"}, {"children": {"improvements": ["Allow folding SystemRole box."]}, "date": "2025-04-18", "version": "1.81.2"}, {"children": {"improvements": ["Add SenseNova-V6 series & SenseChat-Vision support."]}, "date": "2025-04-18", "version": "1.81.1"}, {"children": {"features": ["Support search1api as search provider."]}, "date": "2025-04-17", "version": "1.81.0"}, {"children": {"improvements": ["Update 360 AI & Taichu & AI21 model list."]}, "date": "2025-04-17", "version": "1.80.5"}, {"children": {"improvements": ["Add o3/o4-mini models."]}, "date": "2025-04-17", "version": "1.80.4"}, {"children": {"improvements": ["Refactor to get user info from api."]}, "date": "2025-04-16", "version": "1.80.3"}, {"children": {"fixes": ["Update default file embedding model info."]}, "date": "2025-04-16", "version": "1.80.2"}, {"children": {"fixes": ["Fix model support file and image logic."], "improvements": ["Add step-r1 & glm-z1 model fetch support."]}, "date": "2025-04-16", "version": "1.80.1"}, {"children": {"improvements": ["Opening message container add border."], "features": ["Opening settings copyright i18n, regenerate migrate sql for new agent opening settings."], "fixes": ["Opening questions should should be hidden when no setted, opening settings migration type error."]}, "date": "2025-04-16", "version": "1.80.0"}, {"children": {"improvements": ["Make gpt-4.1-mini default model."]}, "date": "2025-04-15", "version": "1.79.10"}, {"children": {"improvements": ["Add GPT-4.1 models."]}, "date": "2025-04-15", "version": "1.79.9"}, {"children": {"fixes": ["Fix Azure OpenAI unable to process image messages in local s3."]}, "date": "2025-04-12", "version": "1.79.8"}, {"children": {"improvements": ["Refactor hotkey import to avoid db generate error."]}, "date": "2025-04-11", "version": "1.79.7"}, {"children": {"fixes": ["Fix grok-3-mini series calling."]}, "date": "2025-04-11", "version": "1.79.6"}, {"children": {"fixes": ["Fix langfuse intergation."]}, "date": "2025-04-10", "version": "1.79.5"}, {"children": {"improvements": ["Update Grok 3 models."]}, "date": "2025-04-10", "version": "1.79.4"}, {"children": {"fixes": ["Remove Azure AI o3-mini unsupported parameters."]}, "date": "2025-04-10", "version": "1.79.3"}, {"children": {"fixes": ["Fix env for Tencent Cloud & remove deepseek-v3 series fc tag."]}, "date": "2025-04-09", "version": "1.79.2"}, {"children": {"fixes": ["Fix xAI API calling, not support stream_options."]}, "date": "2025-04-09", "version": "1.79.1"}, {"children": {"features": ["Add wiki migrate instructions."]}, "date": "2025-04-09", "version": "1.79.0"}, {"children": {"features": ["Add Keycloak SSO provider support."]}, "date": "2025-04-09", "version": "1.78.0"}, {"children": {"improvements": ["Add time_range & categories support for SearXNG."]}, "date": "2025-04-09", "version": "1.77.18"}, {"children": {"fixes": ["Refactor ollama pull flow and model service."]}, "date": "2025-04-08", "version": "1.77.17"}, {"children": {"improvements": ["Refactor the file service."]}, "date": "2025-04-06", "version": "1.77.16"}, {"children": {"improvements": ["Improve public procedure implement."]}, "date": "2025-04-06", "version": "1.77.15"}, {"children": {"improvements": ["Add ernie-x1-32k-preview support for Wenxin."]}, "date": "2025-04-06", "version": "1.77.14"}, {"children": {"fixes": ["Fix data not show correctly in 1.77.11."]}, "date": "2025-04-06", "version": "1.77.13"}, {"children": {"improvements": ["Fix QVQ Max model, support default config for system agent and pre-merge some desktop code."]}, "date": "2025-04-06", "version": "1.77.12"}, {"children": {"fixes": ["Fix fetch issue in offline mode and make jina crawler first."]}, "date": "2025-04-04", "version": "1.77.11"}, {"children": {}, "date": "2025-04-03", "version": "1.77.10"}, {"children": {"improvements": ["Add QVQ-Max model."]}, "date": "2025-04-03", "version": "1.77.9"}, {"children": {"fixes": ["Add SEARCH1API_CRAWL_API_KEY env."], "improvements": ["Auto refresh TokenTag count."]}, "date": "2025-04-03", "version": "1.77.8"}, {"children": {"improvements": ["Add desktop-release workflow and improve code."]}, "date": "2025-04-03", "version": "1.77.7"}, {"children": {"improvements": ["Refactor the db to context inject mode."]}, "date": "2025-04-01", "version": "1.77.6"}, {"children": {}, "date": "2025-04-01", "version": "1.77.5"}, {"children": {"improvements": ["Update branding."]}, "date": "2025-03-31", "version": "1.77.4"}, {"children": {"improvements": ["Move general db models to database folder."]}, "date": "2025-03-29", "version": "1.77.3"}, {"children": {"fixes": ["Fix decrypt error with imported pg data."]}, "date": "2025-03-29", "version": "1.77.2"}, {"children": {"fixes": ["Fix export button and clean orphan agent."]}, "date": "2025-03-29", "version": "1.77.1"}, {"children": {"features": ["Support pglite and postgres data export."]}, "date": "2025-03-29", "version": "1.77.0"}, {"children": {"improvements": ["Supports OpenAI's latest voice model gpt-4o-mini-tts."]}, "date": "2025-03-29", "version": "1.76.1"}, {"children": {"features": ["Add Hotkey Settings."]}, "date": "2025-03-28", "version": "1.76.0"}, {"children": {"improvements": ["Add tencentcloud deepseek-v3-0324, support for parsing imageOutput, update models for siliconcloud & infiniai."]}, "date": "2025-03-28", "version": "1.75.5"}, {"children": {"improvements": ["Update models info."]}, "date": "2025-03-27", "version": "1.75.4"}, {"children": {"fixes": ["Fix anthropic import issue."]}, "date": "2025-03-26", "version": "1.75.3"}, {"children": {"fixes": ["Update 'gemini-2.5-pro-exp-03-25' maxOutput and contextWindowTokens."]}, "date": "2025-03-26", "version": "1.75.2"}, {"children": {"improvements": ["Update siliconcloud models."]}, "date": "2025-03-26", "version": "1.75.1"}, {"children": {"features": ["Add reasoning content selector and update AutoScroll component, add Xinference provider support."], "improvements": ["Add Gemini 2.5 Pro Experimental model, improve editing scroll experience."]}, "date": "2025-03-26", "version": "1.75.0"}, {"children": {"improvements": ["Upgrade styles for Drawer."]}, "date": "2025-03-25", "version": "1.74.11"}, {"children": {"improvements": ["Add hunyuan-t1-latest from Hunyuan."]}, "date": "2025-03-25", "version": "1.74.10"}, {"children": {"improvements": ["Add reject pattern for browserless to boost crawl performance."]}, "date": "2025-03-25", "version": "1.74.9"}, {"children": {"fixes": ["Update create message loading issue."]}, "date": "2025-03-24", "version": "1.74.8"}, {"children": {"fixes": ["Remove Tooltip component in Topic in mobile mode."]}, "date": "2025-03-24", "version": "1.74.7"}, {"children": {"fixes": ["Set max_completion_tokens to undefined for Azure OpenAI."]}, "date": "2025-03-24", "version": "1.74.6"}, {"children": {"fixes": ["Fix wechat login error with next-auth."]}, "date": "2025-03-23", "version": "1.74.5"}, {"children": {"fixes": ["Upgrade next to 15.2.3 to fix CVE-2025-29927."]}, "date": "2025-03-23", "version": "1.74.4"}, {"children": {"improvements": ["Clear previous model check result."]}, "date": "2025-03-22", "version": "1.74.3"}, {"children": {"fixes": ["CheckModel change clears other configs, update input and output prices."]}, "date": "2025-03-22", "version": "1.74.2"}, {"children": {"improvements": ["Fix deepseek-r1-70b-online search tag missing from Search1API."]}, "date": "2025-03-22", "version": "1.74.1"}, {"children": {"features": ["Add infini-ai provider, add Search1API provider with web search DeepSeek models."]}, "date": "2025-03-21", "version": "1.74.0"}, {"children": {"fixes": ["Fix agent chatConfig override issue."]}, "date": "2025-03-21", "version": "1.73.2"}, {"children": {"improvements": ["Update shiki to v3."]}, "date": "2025-03-21", "version": "1.73.1"}, {"children": {"features": ["Add Cohere provider support, add search1api crawler implementation for WeChat Sogou links."]}, "date": "2025-03-19", "version": "1.73.0"}, {"children": {"fixes": ["Allow historyCount to be set to 0."]}, "date": "2025-03-19", "version": "1.72.1"}, {"children": {"features": ["Update db schema to add user_id for data export."]}, "date": "2025-03-18", "version": "1.72.0"}, {"children": {"improvements": ["Support screenshot to clipboard when sharing."]}, "date": "2025-03-17", "version": "1.71.5"}, {"children": {"improvements": ["Update Wenxin & Hunyuan model list."]}, "date": "2025-03-17", "version": "1.71.4"}, {"children": {"fixes": ["Fix claude 3.5+ models context max output."]}, "date": "2025-03-15", "version": "1.71.3"}, {"children": {"fixes": ["Fix knowledge base issue."]}, "date": "2025-03-15", "version": "1.71.2"}, {"children": {"fixes": ["Fix google gemini output relative issue."], "improvements": ["Update Vertex AI models."]}, "date": "2025-03-15", "version": "1.71.1"}, {"children": {"features": ["Support gemini image output in chat."]}, "date": "2025-03-14", "version": "1.71.0"}, {"children": {}, "date": "2025-03-13", "version": "1.70.11"}, {"children": {"fixes": ["The agent setting -edit_agent not work."]}, "date": "2025-03-12", "version": "1.70.10"}, {"children": {}, "date": "2025-03-12", "version": "1.70.9"}, {"children": {"fixes": ["Fix theme flicking."]}, "date": "2025-03-12", "version": "1.70.8"}, {"children": {"fixes": ["Fix crawl result for short content."]}, "date": "2025-03-12", "version": "1.70.7"}, {"children": {"fixes": ["Link jump in mobile terminal data statistics."]}, "date": "2025-03-11", "version": "1.70.6"}, {"children": {"fixes": ["Refactor the theme implement."]}, "date": "2025-03-11", "version": "1.70.5"}, {"children": {"improvements": ["Support OpenRouter custom BaseURL."]}, "date": "2025-03-11", "version": "1.70.4"}, {"children": {}, "date": "2025-03-11", "version": "1.70.3"}, {"children": {"fixes": ["Update cvpr cvf url rules."]}, "date": "2025-03-10", "version": "1.70.2"}, {"children": {"fixes": ["Fix anthropic max tokens."]}, "date": "2025-03-10", "version": "1.70.1"}, {"children": {"features": ["Support no-fc models like deepseek r1 with online search."]}, "date": "2025-03-09", "version": "1.70.0"}, {"children": {"fixes": ["Fix context cache control and model builtin search switch."]}, "date": "2025-03-09", "version": "1.69.6"}, {"children": {"improvements": ["Support openrouter claude 3.7 sonnet reasoning."]}, "date": "2025-03-09", "version": "1.69.5"}, {"children": {"fixes": ["Fix mistral can not chat."]}, "date": "2025-03-09", "version": "1.69.4"}, {"children": {"improvements": ["Add login ui for next-auth."]}, "date": "2025-03-08", "version": "1.69.3"}, {"children": {"improvements": ["Refactor the agent runtime implement."]}, "date": "2025-03-07", "version": "1.69.2"}, {"children": {"improvements": ["Add Qwen QwQ model."]}, "date": "2025-03-07", "version": "1.69.1"}, {"children": {"features": ["Support Anthropic Context Caching."]}, "date": "2025-03-07", "version": "1.69.0"}, {"children": {"improvements": ["Add Gemini 2.0 Flash model variations, add QwQ models."]}, "date": "2025-03-07", "version": "1.68.11"}, {"children": {"fixes": ["Fix litellm streaming usage and refactor the usage chunk."]}, "date": "2025-03-06", "version": "1.68.10"}, {"children": {"improvements": ["Add epub file chunk split support."]}, "date": "2025-03-05", "version": "1.68.9"}, {"children": {"improvements": ["Improve openrouter models info."]}, "date": "2025-03-05", "version": "1.68.8"}, {"children": {"improvements": ["Refactor agent runtime to better code format."]}, "date": "2025-03-05", "version": "1.68.7"}, {"children": {"fixes": ["Fix custom ai provider sdk type."]}, "date": "2025-03-05", "version": "1.68.6"}, {"children": {"improvements": ["Fix provider order."]}, "date": "2025-03-04", "version": "1.68.5"}, {"children": {"improvements": ["Support to show token usages."]}, "date": "2025-03-04", "version": "1.68.4"}, {"children": {"fixes": ["Improve url rules."]}, "date": "2025-03-03", "version": "1.68.3"}, {"children": {"improvements": ["Add build-in web search support for Wenxin & Hunyuan."]}, "date": "2025-03-03", "version": "1.68.2"}, {"children": {"fixes": ["Fix page crash with crawler error."]}, "date": "2025-03-03", "version": "1.68.1"}, {"children": {"features": ["Add new model provider PPIO."], "fixes": ["Fix search web-browsing display bug."]}, "date": "2025-03-03", "version": "1.68.0"}, {"children": {"fixes": ["Improve some crawl case."]}, "date": "2025-03-02", "version": "1.67.2"}, {"children": {}, "date": "2025-03-02", "version": "1.67.1"}, {"children": {"features": ["Support web page crawl in the search."]}, "date": "2025-03-02", "version": "1.67.0"}, {"children": {"improvements": ["Add gpt-4.5-preview for OpenAI."]}, "date": "2025-03-02", "version": "1.66.6"}, {"children": {"improvements": ["Improve portal style."]}, "date": "2025-02-28", "version": "1.66.5"}, {"children": {"improvements": ["Optimize smooth output."]}, "date": "2025-02-28", "version": "1.66.4"}, {"children": {"fixes": ["Fix fetch assistants plugin error."]}, "date": "2025-02-27", "version": "1.66.3"}, {"children": {"fixes": ["Update Claude sonnet 3.7 model ID."]}, "date": "2025-02-27", "version": "1.66.2"}, {"children": {"improvements": ["Added eu-central-1 region for bedrock."]}, "date": "2025-02-27", "version": "1.66.1"}, {"children": {"features": ["Add online search support for available providers."]}, "date": "2025-02-27", "version": "1.66.0"}, {"children": {"improvements": ["Support parsing the search flag when parsing the model list, Update Gemini & Qwen models."]}, "date": "2025-02-27", "version": "1.65.2"}, {"children": {"fixes": ["Fix claude 3.7 sonnet thinking with tool use."]}, "date": "2025-02-26", "version": "1.65.1"}, {"children": {"features": ["Support claude sonnet 3.7 thinking."], "improvements": ["Update Gemini 2.0 search settings."]}, "date": "2025-02-25", "version": "1.65.0"}, {"children": {"improvements": ["Add Claude 3.7 Sonnet and Haiku 3.5."]}, "date": "2025-02-25", "version": "1.64.3"}, {"children": {"fixes": ["Fix 0 search results with specific search engine."]}, "date": "2025-02-25", "version": "1.64.2"}, {"children": {"fixes": ["Disable fc for ds-v3 series."]}, "date": "2025-02-25", "version": "1.64.1"}, {"children": {"features": ["Support application search with searchXNG."]}, "date": "2025-02-24", "version": "1.64.0"}, {"children": {"fixes": ["Fix citation=null issue in stream."]}, "date": "2025-02-24", "version": "1.63.3"}, {"children": {"fixes": ["Fix model settings config."]}, "date": "2025-02-24", "version": "1.63.2"}, {"children": {"fixes": ["Fix groq location request."], "improvements": ["Improve plugin calling style."]}, "date": "2025-02-23", "version": "1.63.1"}, {"children": {"features": ["Support model-level search for Google/Qwen."], "improvements": ["Update many models info."]}, "date": "2025-02-23", "version": "1.63.0"}, {"children": {"fixes": ["Refine role assignment logic for specific Azure OpenAI models & Sensitive URL."], "improvements": ["Add custom proxyUrl support for Volcengine."]}, "date": "2025-02-23", "version": "1.62.11"}, {"children": {"fixes": ["Fix fetch on client check status display."]}, "date": "2025-02-22", "version": "1.62.10"}, {"children": {"fixes": ["Next-auth user id not found in create agent index."]}, "date": "2025-02-22", "version": "1.62.9"}, {"children": {"fixes": ["Fix image prompts with some user cases."]}, "date": "2025-02-22", "version": "1.62.8"}, {"children": {"improvements": ["Add Volcano Ark models."]}, "date": "2025-02-21", "version": "1.62.7"}, {"children": {"improvements": ["Refactor the plugin render style."]}, "date": "2025-02-21", "version": "1.62.6"}, {"children": {"fixes": ["Fix default agent loading."]}, "date": "2025-02-21", "version": "1.62.5"}, {"children": {"fixes": ["Fix hotkeys of open agent settings."], "improvements": ["Add some error types."]}, "date": "2025-02-20", "version": "1.62.4"}, {"children": {"fixes": ["Fix a feature flag."]}, "date": "2025-02-20", "version": "1.62.3"}, {"children": {"fixes": ["Fix message roles for specific Azure OpenAI models."]}, "date": "2025-02-20", "version": "1.62.2"}, {"children": {"fixes": ["Add sambanova proxy url."]}, "date": "2025-02-20", "version": "1.62.1"}, {"children": {"features": ["Support pplx search grounding."], "fixes": ["Azure AI env var configuration issue.."]}, "date": "2025-02-20", "version": "1.62.0"}, {"children": {"fixes": ["Casdoor webhooks error."]}, "date": "2025-02-20", "version": "1.61.6"}, {"children": {"improvements": ["Show sso providers for next-auth in profile page."]}, "date": "2025-02-19", "version": "1.61.5"}, {"children": {"improvements": ["Improve perplexity models."]}, "date": "2025-02-18", "version": "1.61.4"}, {"children": {"improvements": ["Improve error content and console error."]}, "date": "2025-02-18", "version": "1.61.3"}, {"children": {"improvements": ["Add kimi-latest for Moonshot."]}, "date": "2025-02-18", "version": "1.61.2"}, {"children": {"improvements": ["Improve serveral error code."]}, "date": "2025-02-18", "version": "1.61.1"}, {"children": {"features": ["Support google vertex ai as a new provider."], "fixes": ["Try to fix pglite worker."]}, "date": "2025-02-18", "version": "1.61.0"}, {"children": {}, "date": "2025-02-18", "version": "1.60.9"}, {"children": {"improvements": ["Sync chat limit."]}, "date": "2025-02-18", "version": "1.60.8"}, {"children": {"improvements": ["Remove deprecated gemini models, update MiniMax models."]}, "date": "2025-02-17", "version": "1.60.7"}, {"children": {"improvements": ["Add o1 vision metadata."]}, "date": "2025-02-17", "version": "1.60.6"}, {"children": {"fixes": ["Fix loading on not login for db."]}, "date": "2025-02-17", "version": "1.60.5"}, {"children": {"fixes": ["Fix agent config not load correctly."]}, "date": "2025-02-17", "version": "1.60.4"}, {"children": {"fixes": ["User feedback for empty/long group names in create/edit group modals."]}, "date": "2025-02-17", "version": "1.60.3"}, {"children": {"fixes": ["Fix model list issue in client mode."]}, "date": "2025-02-17", "version": "1.60.2"}, {"children": {"improvements": ["Update Jina AI Provider name & model info."]}, "date": "2025-02-17", "version": "1.60.1"}, {"children": {"features": ["Add SambaNova provider support."]}, "date": "2025-02-17", "version": "1.60.0"}, {"children": {"features": ["Add volcengine as a new provider."]}, "date": "2025-02-16", "version": "1.59.0"}, {"children": {"features": ["Add Azure AI as new Provider."]}, "date": "2025-02-16", "version": "1.58.0"}, {"children": {"improvements": ["Fix mobile agent settings not show correctly."]}, "date": "2025-02-16", "version": "1.57.1"}, {"children": {"features": ["Add Jina AI model provider support."]}, "date": "2025-02-16", "version": "1.57.0"}, {"children": {"fixes": ["Match o1 series models more robust in Azure OpenAI provider, set max_completion_tokens to null for Azure OpenAI."]}, "date": "2025-02-16", "version": "1.56.5"}, {"children": {"fixes": ["Fix ai provider description not show correctly."]}, "date": "2025-02-16", "version": "1.56.4"}, {"children": {"improvements": ["Improve inbox agent settings."]}, "date": "2025-02-16", "version": "1.56.3"}, {"children": {"fixes": ["Fix inbox agent can not save config."]}, "date": "2025-02-16", "version": "1.56.2"}, {"children": {"fixes": ["Fix inbox agent edit way in the new mode."]}, "date": "2025-02-16", "version": "1.56.1"}, {"children": {"features": ["Add configurable PDF processing method with Unstructured."]}, "date": "2025-02-15", "version": "1.56.0"}, {"children": {"improvements": ["Improve mobile params style."]}, "date": "2025-02-15", "version": "1.55.4"}, {"children": {"improvements": ["Add deepseek r1 distill models for qwen series."]}, "date": "2025-02-15", "version": "1.55.3"}, {"children": {"fixes": ["Avoid blank reasoning with OpenRouter."]}, "date": "2025-02-15", "version": "1.55.2"}, {"children": {"fixes": ["Fix Azure OpenAI O1 models and refactor the Azure OpenAI implement."], "improvements": ["Update openrouter model list and descriptions."]}, "date": "2025-02-15", "version": "1.55.1"}, {"children": {"features": ["Add vLLM provider support."]}, "date": "2025-02-14", "version": "1.55.0"}, {"children": {"features": ["Add Nvidia NIM provider support."], "improvements": ["Improve advanced params settings."]}, "date": "2025-02-14", "version": "1.54.0"}, {"children": {"improvements": ["Improve model fetch behavior."]}, "date": "2025-02-14", "version": "1.53.12"}, {"children": {"fixes": ["Fix provider form api key."]}, "date": "2025-02-13", "version": "1.53.11"}, {"children": {"fixes": ["Fix api key input issue."]}, "date": "2025-02-13", "version": "1.53.10"}, {"children": {"improvements": ["Support select check models."]}, "date": "2025-02-13", "version": "1.53.9"}, {"children": {"fixes": ["Fix model fetch for spark and fix the support of model reset."]}, "date": "2025-02-13", "version": "1.53.8"}, {"children": {"improvements": ["Update model list."]}, "date": "2025-02-13", "version": "1.53.7"}, {"children": {"fixes": ["Fix not enable models correctly."]}, "date": "2025-02-13", "version": "1.53.6"}, {"children": {"fixes": ["Fix latex in thinking tag render."]}, "date": "2025-02-13", "version": "1.53.5"}, {"children": {"fixes": ["Fix ai model abilities issue."]}, "date": "2025-02-12", "version": "1.53.4"}, {"children": {"fixes": ["Fix tencent cloud api issue."]}, "date": "2025-02-12", "version": "1.53.3"}, {"children": {"fixes": ["Disable openrouter client fetch."]}, "date": "2025-02-12", "version": "1.53.2"}, {"children": {"fixes": ["Fix reasoning output for OpenRouter reasoning models like deepseek-r1."]}, "date": "2025-02-12", "version": "1.53.1"}, {"children": {"features": ["Support tencent cloud provider."], "improvements": ["Update i18n, update provider i18n."]}, "date": "2025-02-11", "version": "1.53.0"}, {"children": {"improvements": ["Refactor the agent runtime test case."]}, "date": "2025-02-11", "version": "1.52.19"}, {"children": {}, "date": "2025-02-11", "version": "1.52.18"}, {"children": {}, "date": "2025-02-11", "version": "1.52.17"}, {"children": {"improvements": ["Support mistral proxy url."]}, "date": "2025-02-11", "version": "1.52.16"}, {"children": {"fixes": ["Fix lmstudio baseURL."], "improvements": ["Optimized MaxToken Slider."]}, "date": "2025-02-10", "version": "1.52.15"}, {"children": {"improvements": ["Refactor agent settings modal."]}, "date": "2025-02-10", "version": "1.52.14"}, {"children": {"fixes": ["Fix <PERSON> deepseek-r1 reasoning parsing with oneapi, Support <PERSON><PERSON> deepseek-r1 reasoning."]}, "date": "2025-02-10", "version": "1.52.13"}, {"children": {"fixes": ["Fix language incorrect on page hydration."]}, "date": "2025-02-10", "version": "1.52.12"}, {"children": {"improvements": ["Support Mermaid in Artifacts."]}, "date": "2025-02-10", "version": "1.52.11"}, {"children": {}, "date": "2025-02-09", "version": "1.52.10"}, {"children": {"fixes": ["Fix changelog issue."]}, "date": "2025-02-09", "version": "1.52.9"}, {"children": {"improvements": ["Update github model list, Update openrouter model list."]}, "date": "2025-02-09", "version": "1.52.8"}, {"children": {"fixes": ["Rewrite to local container in docker deployment mode."], "improvements": ["Update Cloudflare models."]}, "date": "2025-02-09", "version": "1.52.7"}, {"children": {"improvements": ["Update ZeroOne models."]}, "date": "2025-02-08", "version": "1.52.6"}, {"children": {"fixes": ["Fix changelog modal."]}, "date": "2025-02-08", "version": "1.52.5"}, {"children": {"fixes": ["Fix changelog modal."]}, "date": "2025-02-08", "version": "1.52.4"}, {"children": {"fixes": ["Add Zhipu param limit, Fix translation in variants mode."], "improvements": ["Update Gemini 2.0 models."]}, "date": "2025-02-08", "version": "1.52.3"}, {"children": {"improvements": ["Add siliconcloud pro models."]}, "date": "2025-02-08", "version": "1.52.2"}, {"children": {"fixes": ["Fix static relative issues."]}, "date": "2025-02-08", "version": "1.52.1"}, {"children": {"features": ["Refactor the auth condition in Next Auth."]}, "date": "2025-02-08", "version": "1.52.0"}, {"children": {}, "date": "2025-02-07", "version": "1.51.16"}, {"children": {"fixes": ["Fix home next auth error and update pnpm."]}, "date": "2025-02-07", "version": "1.51.15"}, {"children": {"improvements": ["Update changelog cache and upgrade anthropic sdk."]}, "date": "2025-02-07", "version": "1.51.14"}, {"children": {"fixes": ["<PERSON><PERSON> next auth error."]}, "date": "2025-02-07", "version": "1.51.13"}, {"children": {"fixes": ["Try to fix next-auth issue."]}, "date": "2025-02-07", "version": "1.51.12"}, {"children": {"fixes": ["Fix /file/[id] 500 issue."]}, "date": "2025-02-06", "version": "1.51.11"}, {"children": {"fixes": ["Fix provider 500 issue."]}, "date": "2025-02-06", "version": "1.51.10"}, {"children": {"improvements": ["Update edtion tag display and improve prerender."]}, "date": "2025-02-06", "version": "1.51.9"}, {"children": {"improvements": ["Refactor model fetch method."]}, "date": "2025-02-06", "version": "1.51.8"}, {"children": {"improvements": ["Add Aliyun deepseek-r1 distill models."]}, "date": "2025-02-06", "version": "1.51.7"}, {"children": {"fixes": ["Try to fix discover error."]}, "date": "2025-02-06", "version": "1.51.6"}, {"children": {"improvements": ["Add siliconcloud models."]}, "date": "2025-02-06", "version": "1.51.5"}, {"children": {}, "date": "2025-02-06", "version": "1.51.4"}, {"children": {"improvements": ["<PERSON><PERSON>, <PERSON><PERSON><PERSON>, FeatureFlag Viewer to DevPanel."], "fixes": ["Artifact Parsing and Rendering Bug Fix for Gemini 2.0 Flash."]}, "date": "2025-02-05", "version": "1.51.3"}, {"children": {"improvements": ["Update model list, add reasoning tag."]}, "date": "2025-02-05", "version": "1.51.2"}, {"children": {}, "date": "2025-02-05", "version": "1.51.1"}, {"children": {"features": ["Add reasoning tag support for custom models via UI or ENV."], "fixes": ["Fix deepseek-v3 & qvq model tag fetch error from SiliconCloud, fix model ability missing."]}, "date": "2025-02-05", "version": "1.51.0"}, {"children": {"improvements": ["Add/Update Aliyun Cloud Models, update GitHub Models."]}, "date": "2025-02-04", "version": "1.50.5"}, {"children": {"fixes": ["Fix invalid utf8 character."]}, "date": "2025-02-04", "version": "1.50.4"}, {"children": {"improvements": ["Update model locale."]}, "date": "2025-02-04", "version": "1.50.3"}, {"children": {"fixes": ["Fix o1 series calling issue."]}, "date": "2025-02-04", "version": "1.50.2"}, {"children": {"fixes": ["Bind the selected group name in the rename modal.."]}, "date": "2025-02-03", "version": "1.50.1"}, {"children": {"features": ["Add o3-mini support for OpenAI & GitHub Models."], "fixes": ["Fix parse of deepseek r1 in siliconflow provider."]}, "date": "2025-02-03", "version": "1.50.0"}, {"children": {"improvements": ["Update perplexity models."]}, "date": "2025-02-03", "version": "1.49.16"}, {"children": {"improvements": ["Update Fireworks check model and fix check error."]}, "date": "2025-02-03", "version": "1.49.15"}, {"children": {"fixes": ["Fix provider update issue."]}, "date": "2025-02-03", "version": "1.49.14"}, {"children": {"fixes": ["Optimize requests without historical messages."]}, "date": "2025-02-03", "version": "1.49.13"}, {"children": {"fixes": ["Fix can not stop generating."]}, "date": "2025-02-02", "version": "1.49.12"}, {"children": {"fixes": ["Fix ollama intergration checker and client fetch issue."]}, "date": "2025-02-02", "version": "1.49.11"}, {"children": {"fixes": ["Fix <think> tag crash with special markdown content."]}, "date": "2025-02-02", "version": "1.49.10"}, {"children": {"improvements": ["Update siliconcloud models."]}, "date": "2025-02-01", "version": "1.49.9"}, {"children": {"improvements": ["Support thinking for all non DeepSeek official api R1 models."]}, "date": "2025-02-01", "version": "1.49.8"}, {"children": {"fixes": ["Multiple deepseek-reasoner request errors."]}, "date": "2025-02-01", "version": "1.49.7"}, {"children": {"fixes": ["Support litellm reasoning streaming."]}, "date": "2025-01-30", "version": "1.49.6"}, {"children": {"fixes": ["Pin @clerk/nextjs@6.10.2 to avoid build error."]}, "date": "2025-01-28", "version": "1.49.5"}, {"children": {"fixes": ["Fix changelog locale not showing English."]}, "date": "2025-01-28", "version": "1.49.4"}, {"children": {"fixes": ["Fix discover ssr hydration error."]}, "date": "2025-01-27", "version": "1.49.3"}, {"children": {"improvements": ["Remove use query."]}, "date": "2025-01-27", "version": "1.49.2"}, {"children": {"improvements": ["UseMobileWorkspace use nqus to replace useQuery."]}, "date": "2025-01-27", "version": "1.49.1"}, {"children": {"features": ["Support Doubao Models."]}, "date": "2025-01-27", "version": "1.49.0"}, {"children": {"improvements": ["Improve thinking style."]}, "date": "2025-01-27", "version": "1.48.4"}, {"children": {"improvements": ["Improve model pricing with CNY."]}, "date": "2025-01-26", "version": "1.48.3"}, {"children": {"improvements": ["Add parallel_tool_calls support for Qwen, fix tag version and add provider changelog."]}, "date": "2025-01-25", "version": "1.48.2"}, {"children": {"fixes": ["Fix ollama Browser Request failed in PG mode."]}, "date": "2025-01-25", "version": "1.48.1"}, {"children": {"features": ["Support display thinking for DeepSeek R1."]}, "date": "2025-01-24", "version": "1.48.0"}, {"children": {"improvements": ["Fix model fetch match tag error & add Hunyuan model fetch support."]}, "date": "2025-01-24", "version": "1.47.23"}, {"children": {"fixes": ["Fix form input in provider."]}, "date": "2025-01-24", "version": "1.47.22"}, {"children": {"improvements": ["Add HuggingFace Model: DeepSeek R1."]}, "date": "2025-01-23", "version": "1.47.21"}, {"children": {"fixes": ["Fix tts in new provider model."]}, "date": "2025-01-23", "version": "1.47.20"}, {"children": {"improvements": ["Add new stepfun model."]}, "date": "2025-01-23", "version": "1.47.19"}, {"children": {"fixes": ["Fix debounce issue of provider config."]}, "date": "2025-01-23", "version": "1.47.18"}, {"children": {"fixes": ["Upgrade react-i18next to ^15."]}, "date": "2025-01-22", "version": "1.47.17"}, {"children": {"improvements": ["Add gemini new model."]}, "date": "2025-01-22", "version": "1.47.16"}, {"children": {"improvements": ["Improve discover model page."]}, "date": "2025-01-22", "version": "1.47.15"}, {"children": {"improvements": ["Support model list with model fetcher settings."]}, "date": "2025-01-22", "version": "1.47.14"}, {"children": {"improvements": ["Add ModelFetcher for supported providers."]}, "date": "2025-01-21", "version": "1.47.13"}, {"children": {"improvements": ["Refactor [@nav](https://github.com/nav) layout and improve pin list style."]}, "date": "2025-01-21", "version": "1.47.12"}, {"children": {"improvements": ["Improve code for ai provider."]}, "date": "2025-01-21", "version": "1.47.11"}, {"children": {"improvements": ["Support assistant blacklist."]}, "date": "2025-01-21", "version": "1.47.10"}, {"children": {"improvements": ["Improve error code."]}, "date": "2025-01-20", "version": "1.47.9"}, {"children": {"improvements": ["Add deepseek r1 model."]}, "date": "2025-01-20", "version": "1.47.8"}, {"children": {"improvements": ["Remove redundant payload remapping in client-fetch."]}, "date": "2025-01-20", "version": "1.47.7"}, {"children": {"improvements": ["Refactor provider code."]}, "date": "2025-01-20", "version": "1.47.6"}, {"children": {"improvements": ["Improve ai provider code."]}, "date": "2025-01-20", "version": "1.47.5"}, {"children": {}, "date": "2025-01-18", "version": "1.47.4"}, {"children": {"fixes": ["Fix hydration error."]}, "date": "2025-01-18", "version": "1.47.3"}, {"children": {"fixes": ["Fix api key in api key form."]}, "date": "2025-01-17", "version": "1.47.2"}, {"children": {}, "date": "2025-01-17", "version": "1.47.1"}, {"children": {"features": ["Support new ai provider in client pglite."]}, "date": "2025-01-17", "version": "1.47.0"}, {"children": {"fixes": ["Improve validation for provider and model in parseFilesConfig, temporarily disable S3 client integrity check for Cloudflare R2."]}, "date": "2025-01-17", "version": "1.46.7"}, {"children": {"fixes": ["Gemini models HarmBlockThreshold."]}, "date": "2025-01-16", "version": "1.46.6"}, {"children": {}, "date": "2025-01-16", "version": "1.46.5"}, {"children": {"improvements": ["Refactor some implement for the next performance improvement."]}, "date": "2025-01-16", "version": "1.46.4"}, {"children": {"fixes": ["Fix azure in new ai provider."]}, "date": "2025-01-15", "version": "1.46.3"}, {"children": {}, "date": "2025-01-15", "version": "1.46.2"}, {"children": {"improvements": ["Add auth support for PROXY_URL."]}, "date": "2025-01-15", "version": "1.46.1"}, {"children": {"features": ["Add lm studio provider, support to customize Embedding model with env."]}, "date": "2025-01-15", "version": "1.46.0"}, {"children": {"improvements": ["Refactor Minimax with LobeOpenAICompatibleFactory."]}, "date": "2025-01-15", "version": "1.45.17"}, {"children": {"improvements": ["Improve ai provider code."]}, "date": "2025-01-14", "version": "1.45.16"}, {"children": {"fixes": ["Fix pull models error in new ai provider."]}, "date": "2025-01-14", "version": "1.45.15"}, {"children": {}, "date": "2025-01-14", "version": "1.45.14"}, {"children": {"improvements": ["Improve model config form modal."]}, "date": "2025-01-14", "version": "1.45.13"}, {"children": {"fixes": ["Fix enable_search parameter intro condition in Qwen."]}, "date": "2025-01-14", "version": "1.45.12"}, {"children": {"fixes": ["Support Gemini 2.0 HarmBlockThreshold."]}, "date": "2025-01-14", "version": "1.45.11"}, {"children": {"fixes": ["Fix some providers issues."]}, "date": "2025-01-14", "version": "1.45.10"}, {"children": {"fixes": ["Fix pin package manager to pnpm@9 for docker."]}, "date": "2025-01-14", "version": "1.45.9"}, {"children": {"fixes": ["Refactor dynamic import in RSC."]}, "date": "2025-01-14", "version": "1.45.8"}, {"children": {"fixes": ["Fix released at for undefined condition."]}, "date": "2025-01-13", "version": "1.45.7"}, {"children": {"fixes": ["Fix *_MODEL_LIST env in new provider."]}, "date": "2025-01-10", "version": "1.45.6"}, {"children": {"fixes": ["Revert officeparser."]}, "date": "2025-01-09", "version": "1.45.5"}, {"children": {"fixes": ["Fix GitHub and huggingface provider config unusable."]}, "date": "2025-01-09", "version": "1.45.4"}, {"children": {"fixes": ["Fix some ai provider known issues."]}, "date": "2025-01-09", "version": "1.45.3"}, {"children": {"improvements": ["Update siliconcloud model list."]}, "date": "2025-01-09", "version": "1.45.2"}, {"children": {"fixes": ["Fix remark gfm regex breaks in Safari versions < 16.4."]}, "date": "2025-01-09", "version": "1.45.1"}, {"children": {"features": ["Update Remark."]}, "date": "2025-01-08", "version": "1.45.0"}, {"children": {"fixes": ["Fix provider enabled issue."]}, "date": "2025-01-08", "version": "1.44.3"}, {"children": {"fixes": ["Add provider id validate."]}, "date": "2025-01-08", "version": "1.44.2"}, {"children": {"fixes": ["Fix model select not auto update and sort issue."]}, "date": "2025-01-08", "version": "1.44.1"}, {"children": {"features": ["Brand new AI provider."]}, "date": "2025-01-07", "version": "1.44.0"}, {"children": {"fixes": ["Fix portal suspense error when first open."]}, "date": "2025-01-07", "version": "1.43.6"}, {"children": {"improvements": ["Fix style warning in antd 5.23.0 and some error logs."]}, "date": "2025-01-07", "version": "1.43.5"}, {"children": {"fixes": ["Fix format short number."]}, "date": "2025-01-06", "version": "1.43.4"}, {"children": {"improvements": ["Upgrade @clerk/nextjs to v6."]}, "date": "2025-01-04", "version": "1.43.3"}, {"children": {"fixes": ["Fix heatmap and manifest."]}, "date": "2025-01-04", "version": "1.43.2"}, {"children": {"fixes": ["Fix stats data query issue."]}, "date": "2025-01-03", "version": "1.43.1"}, {"children": {"features": ["Add User Stats and Refactor Profile."], "fixes": ["Fix chat page error."]}, "date": "2025-01-03", "version": "1.43.0"}, {"children": {"improvements": ["Fix zero-sized element in topic list."]}, "date": "2025-01-03", "version": "1.42.6"}, {"children": {"fixes": ["Fix topic mobile view ui error."]}, "date": "2025-01-02", "version": "1.42.5"}, {"children": {"improvements": ["Refactor provider info and improve settings side bar loading."]}, "date": "2025-01-02", "version": "1.42.4"}, {"children": {}, "date": "2025-01-01", "version": "1.42.3"}, {"children": {"improvements": ["Add o1 model in openai and openrouter models."]}, "date": "2024-12-31", "version": "1.42.2"}, {"children": {"fixes": ["Fix custom max_token not saved from customModelCards."]}, "date": "2024-12-29", "version": "1.42.1"}, {"children": {"features": ["Add custom stream handle support for LobeOpenAICompatibleFactory."]}, "date": "2024-12-29", "version": "1.42.0"}, {"children": {"features": ["Support white list for discover assistant."]}, "date": "2024-12-28", "version": "1.41.0"}, {"children": {"improvements": ["Update deepseek V3 model."]}, "date": "2024-12-28", "version": "1.40.4"}, {"children": {"fixes": ["Fix fetch error in changelog modal."]}, "date": "2024-12-26", "version": "1.40.3"}, {"children": {"improvements": ["Refactor tokens to contextWindowTokens."]}, "date": "2024-12-26", "version": "1.40.2"}, {"children": {"fixes": ["Fix o1Models list."]}, "date": "2024-12-26", "version": "1.40.1"}, {"children": {"improvements": ["Refactor services code style."], "features": ["Add changelog modal."]}, "date": "2024-12-26", "version": "1.40.0"}, {"children": {"improvements": ["Improve loading brand."]}, "date": "2024-12-25", "version": "1.39.3"}, {"children": {"improvements": ["Refactor sensenova provider with LobeOpenAICompatibleFactory."]}, "date": "2024-12-25", "version": "1.39.2"}, {"children": {"fixes": ["Fix image input on pglite."]}, "date": "2024-12-24", "version": "1.39.1"}, {"children": {"features": ["Upgrade to next15 and react19."]}, "date": "2024-12-23", "version": "1.39.0"}, {"children": {"features": ["Support thread in client pglite."]}, "date": "2024-12-23", "version": "1.38.0"}, {"children": {"improvements": ["Move pglite to client service."]}, "date": "2024-12-22", "version": "1.37.2"}, {"children": {"improvements": ["Refactor the client service to deprecated."]}, "date": "2024-12-22", "version": "1.37.1"}, {"children": {"features": ["Support to use pglite as client db."]}, "date": "2024-12-22", "version": "1.37.0"}, {"children": {"improvements": ["Refactor client mode upload to match server mode."]}, "date": "2024-12-21", "version": "1.36.46"}, {"children": {"improvements": ["Add o1 model in GitHub models."]}, "date": "2024-12-21", "version": "1.36.45"}, {"children": {"improvements": ["Add Gemini flash thinking model."]}, "date": "2024-12-21", "version": "1.36.44"}, {"children": {}, "date": "2024-12-21", "version": "1.36.43"}, {"children": {"fixes": ["Fix HUGGINGFACE endpoint url."]}, "date": "2024-12-21", "version": "1.36.42"}, {"children": {"improvements": ["Upgrade react scan."]}, "date": "2024-12-21", "version": "1.36.41"}, {"children": {"improvements": ["Seperate user keyVaults encrpyto from user model."]}, "date": "2024-12-20", "version": "1.36.40"}, {"children": {"improvements": ["Refactor to use async headers()."]}, "date": "2024-12-20", "version": "1.36.39"}, {"children": {"improvements": ["Refactor layout props."]}, "date": "2024-12-20", "version": "1.36.38"}, {"children": {}, "date": "2024-12-19", "version": "1.36.37"}, {"children": {}, "date": "2024-12-19", "version": "1.36.36"}, {"children": {"improvements": ["Improve home page loading for better UX."]}, "date": "2024-12-18", "version": "1.36.35"}, {"children": {"fixes": ["Fix pdf preview with capital ext."]}, "date": "2024-12-18", "version": "1.36.34"}, {"children": {"fixes": ["Fix GitHub model fetch."]}, "date": "2024-12-18", "version": "1.36.33"}, {"children": {"improvements": ["Refactor the drizzle code style."]}, "date": "2024-12-17", "version": "1.36.32"}, {"children": {"improvements": ["Refactor the data fetch with clientDB init check."]}, "date": "2024-12-17", "version": "1.36.31"}, {"children": {"improvements": ["Improve page loading state."]}, "date": "2024-12-16", "version": "1.36.30"}, {"children": {"fixes": ["Fix discover locale with different default lang."]}, "date": "2024-12-16", "version": "1.36.29"}, {"children": {}, "date": "2024-12-16", "version": "1.36.28"}, {"children": {"fixes": ["Add unique keys to <ModelList> children."]}, "date": "2024-12-16", "version": "1.36.27"}, {"children": {"improvements": ["Update models of Gitee AI provider."]}, "date": "2024-12-16", "version": "1.36.26"}, {"children": {"improvements": ["Add new grok models."]}, "date": "2024-12-14", "version": "1.36.25"}, {"children": {"improvements": ["Refactor file Url query in message model."]}, "date": "2024-12-14", "version": "1.36.24"}, {"children": {"improvements": ["Support csv chunking."]}, "date": "2024-12-13", "version": "1.36.23"}, {"children": {}, "date": "2024-12-13", "version": "1.36.22"}, {"children": {}, "date": "2024-12-13", "version": "1.36.21"}, {"children": {"improvements": ["Update locale."]}, "date": "2024-12-13", "version": "1.36.20"}, {"children": {"fixes": ["One of Gemini functionCall error."]}, "date": "2024-12-13", "version": "1.36.19"}, {"children": {"fixes": ["Fix claude first message can not be assistant."]}, "date": "2024-12-12", "version": "1.36.18"}, {"children": {}, "date": "2024-12-12", "version": "1.36.17"}, {"children": {"improvements": ["Refactor the file model method."]}, "date": "2024-12-12", "version": "1.36.16"}, {"children": {"improvements": ["Enable googleSearch Tool for gemini-2.0-flash-exp."]}, "date": "2024-12-12", "version": "1.36.15"}, {"children": {"improvements": ["Refactor database file model to remove server env."]}, "date": "2024-12-12", "version": "1.36.14"}, {"children": {"improvements": ["Add Gemini 2.0 Flash Exp model."]}, "date": "2024-12-11", "version": "1.36.13"}, {"children": {"improvements": ["Update sql and types."]}, "date": "2024-12-11", "version": "1.36.12"}, {"children": {"improvements": ["Refactor data importer to repos."]}, "date": "2024-12-11", "version": "1.36.11"}, {"children": {"improvements": ["Clean the gpt-4-vision-preview models."]}, "date": "2024-12-10", "version": "1.36.10"}, {"children": {"improvements": ["Refactor the clerk user service implement."], "fixes": ["Nullptr errors in NextAuth adapter."]}, "date": "2024-12-10", "version": "1.36.9"}, {"children": {"improvements": ["Add GLM-4V-Flash from Zhipu."]}, "date": "2024-12-10", "version": "1.36.8"}, {"children": {"fixes": ["Fix pricing with 0 digit."]}, "date": "2024-12-10", "version": "1.36.7"}, {"children": {"improvements": ["Update groq, add llama3.3, Upgrade lobe-ui."]}, "date": "2024-12-10", "version": "1.36.6"}, {"children": {"fixes": ["Fix wrong email linking in next-auth db adapter."]}, "date": "2024-12-09", "version": "1.36.5"}, {"children": {}, "date": "2024-12-09", "version": "1.36.4"}, {"children": {"fixes": ["Support request headers for chat."]}, "date": "2024-12-08", "version": "1.36.3"}, {"children": {"improvements": ["Refactor async params route to adapt next15 breaking change."]}, "date": "2024-12-07", "version": "1.36.2"}, {"children": {"improvements": ["Add gemini-exp-1206 model."]}, "date": "2024-12-07", "version": "1.36.1"}, {"children": {"features": ["Add Higress ai model provider."]}, "date": "2024-12-06", "version": "1.36.0"}, {"children": {"improvements": ["Refactor page params to adapt next15 breaking change."]}, "date": "2024-12-06", "version": "1.35.14"}, {"children": {}, "date": "2024-12-06", "version": "1.35.13"}, {"children": {"fixes": ["Fix typo of prompts."]}, "date": "2024-12-05", "version": "1.35.12"}, {"children": {}, "date": "2024-12-04", "version": "1.35.11"}, {"children": {"improvements": ["Refactor the server db model implement."]}, "date": "2024-12-03", "version": "1.35.10"}, {"children": {}, "date": "2024-12-03", "version": "1.35.9"}, {"children": {"improvements": ["Move schema and migration folder."]}, "date": "2024-12-03", "version": "1.35.8"}, {"children": {}, "date": "2024-12-03", "version": "1.35.7"}, {"children": {"improvements": ["Add QwQ 32B Preview model."]}, "date": "2024-12-02", "version": "1.35.6"}, {"children": {"improvements": ["Deprecated the current client mode code."]}, "date": "2024-12-02", "version": "1.35.5"}, {"children": {}, "date": "2024-12-02", "version": "1.35.4"}, {"children": {"improvements": ["Add gpt-4o-2024-11-20 model."]}, "date": "2024-12-01", "version": "1.35.3"}, {"children": {"improvements": ["Improve i18n."]}, "date": "2024-12-01", "version": "1.35.2"}, {"children": {"improvements": ["Update ollama models."]}, "date": "2024-12-01", "version": "1.35.1"}, {"children": {"features": ["Support ollama tools use."]}, "date": "2024-12-01", "version": "1.35.0"}, {"children": {"improvements": ["Add QWEN_PROXY_URL support for Qwen, update model list, add qwq-32b-preview."]}, "date": "2024-12-01", "version": "1.34.6"}, {"children": {"improvements": ["Add Google LearnLM model."]}, "date": "2024-11-28", "version": "1.34.5"}, {"children": {"improvements": ["Add switch portal thread."]}, "date": "2024-11-27", "version": "1.34.4"}, {"children": {"fixes": ["Fix fallback behavior of default mode in AgentRuntime."]}, "date": "2024-11-27", "version": "1.34.3"}, {"children": {"improvements": ["Improve thread i18n locale."]}, "date": "2024-11-27", "version": "1.34.2"}, {"children": {"fixes": ["<PERSON>x <PERSON> baseUrl calling."]}, "date": "2024-11-26", "version": "1.34.1"}, {"children": {"features": ["Forkable Chat Mode."]}, "date": "2024-11-26", "version": "1.34.0"}, {"children": {"improvements": ["Update the description translation of Gitee AI."]}, "date": "2024-11-26", "version": "1.33.5"}, {"children": {"improvements": ["Refactor getLlmOptionsFromPayload from AgentRuntime."]}, "date": "2024-11-26", "version": "1.33.4"}, {"children": {"fixes": ["Fix fetchOnClient functional for Moonshot."]}, "date": "2024-11-25", "version": "1.33.3"}, {"children": {"fixes": ["Fix multi-turns tools calling."]}, "date": "2024-11-25", "version": "1.33.2"}, {"children": {"improvements": ["Add gemini-exp-1121 model."]}, "date": "2024-11-25", "version": "1.33.1"}, {"children": {"features": ["Add Gitee AI model provider."]}, "date": "2024-11-25", "version": "1.33.0"}, {"children": {"improvements": ["Support to reset fetched models."]}, "date": "2024-11-24", "version": "1.32.9"}, {"children": {"fixes": ["Fix XAI_PROXY_URL env missing."]}, "date": "2024-11-24", "version": "1.32.8"}, {"children": {"fixes": ["Fix tool message display."]}, "date": "2024-11-24", "version": "1.32.7"}, {"children": {}, "date": "2024-11-24", "version": "1.32.6"}, {"children": {"improvements": ["Refactor the main chat."]}, "date": "2024-11-24", "version": "1.32.5"}, {"children": {"improvements": ["Refactor the default locale."]}, "date": "2024-11-20", "version": "1.32.4"}, {"children": {"improvements": ["Add grok-vision-beta model, update Mistral model list, add pixtral-large-latest."]}, "date": "2024-11-20", "version": "1.32.3"}, {"children": {}, "date": "2024-11-19", "version": "1.32.2"}, {"children": {"fixes": ["Keyword search for chat history & sessions."], "improvements": ["Support o1 models using streaming."]}, "date": "2024-11-19", "version": "1.32.1"}, {"children": {"features": ["Add support InternLM (书生浦语) provider."]}, "date": "2024-11-19", "version": "1.32.0"}, {"children": {"fixes": ["Connection check logic."]}, "date": "2024-11-18", "version": "1.31.11"}, {"children": {}, "date": "2024-11-16", "version": "1.31.10"}, {"children": {"improvements": ["Add gemini-exp-1114 model."]}, "date": "2024-11-16", "version": "1.31.9"}, {"children": {"improvements": ["Move ChatInput to features/ChatInput."]}, "date": "2024-11-15", "version": "1.31.8"}, {"children": {"improvements": ["genServerLLMConfig function, get *_MODEL_LIST from env."]}, "date": "2024-11-15", "version": "1.31.7"}, {"children": {"improvements": ["Refactor the chat conversation implement."]}, "date": "2024-11-13", "version": "1.31.6"}, {"children": {"improvements": ["Update some provider modellist & fix ai360 baseurl."]}, "date": "2024-11-12", "version": "1.31.5"}, {"children": {"improvements": ["Fix Cloudflare Workers AI Sort."]}, "date": "2024-11-12", "version": "1.31.4"}, {"children": {"improvements": ["Refactor languageModel & DEFAULT_LLM_CONFIG generate."]}, "date": "2024-11-12", "version": "1.31.3"}, {"children": {"improvements": ["Update deepseek model."]}, "date": "2024-11-12", "version": "1.31.2"}, {"children": {"improvements": ["Fix Windows always showing scrollbar."]}, "date": "2024-11-12", "version": "1.31.1"}, {"children": {"features": ["Add support xAI provider."]}, "date": "2024-11-11", "version": "1.31.0"}, {"children": {"features": ["Support Cloudflare Workers AI."]}, "date": "2024-11-11", "version": "1.30.0"}, {"children": {"improvements": ["Add Sonnet 3.5 v2 inference model to BedRock."]}, "date": "2024-11-10", "version": "1.29.6"}, {"children": {"fixes": ["Fix summary range."]}, "date": "2024-11-10", "version": "1.29.5"}, {"children": {"fixes": ["Disregard remoteModelCards when showModelFetcher is disabled."]}, "date": "2024-11-09", "version": "1.29.4"}, {"children": {"fixes": ["Fix the display model of history summary."]}, "date": "2024-11-09", "version": "1.29.3"}, {"children": {"improvements": ["Allow users to disable SSRF or set a whitelist."]}, "date": "2024-11-09", "version": "1.29.2"}, {"children": {"fixes": ["Fix topic summary field on server db."]}, "date": "2024-11-09", "version": "1.29.1"}, {"children": {"features": ["Support compress history messages."]}, "date": "2024-11-09", "version": "1.29.0"}, {"children": {"fixes": ["Fix env typo of MS Entra ID."]}, "date": "2024-11-09", "version": "1.28.6"}, {"children": {}, "date": "2024-11-08", "version": "1.28.5"}, {"children": {"fixes": ["Disable model fetch for GitHub."]}, "date": "2024-11-07", "version": "1.28.4"}, {"children": {"improvements": ["Move portal code to features folder."]}, "date": "2024-11-06", "version": "1.28.3"}, {"children": {"improvements": ["Refactor and clean some code."]}, "date": "2024-11-06", "version": "1.28.2"}, {"children": {"improvements": ["Update database fields."]}, "date": "2024-11-06", "version": "1.28.1"}, {"children": {"features": ["Support export as markdown and JSON."]}, "date": "2024-11-05", "version": "1.28.0"}, {"children": {"improvements": ["Add claude 3.5 haiku model."]}, "date": "2024-11-05", "version": "1.27.3"}, {"children": {"fixes": ["Remove the 'resetConversation' hot key tip."], "improvements": ["Improve group topic styles."]}, "date": "2024-11-05", "version": "1.27.2"}, {"children": {"fixes": ["Fix /webapi/plugin/store server error."]}, "date": "2024-11-04", "version": "1.27.1"}, {"children": {"features": ["Support group topics by time."]}, "date": "2024-11-04", "version": "1.27.0"}, {"children": {"fixes": ["If enable login and not signed in, return unauthorized error."]}, "date": "2024-11-04", "version": "1.26.21"}, {"children": {"improvements": ["Disable chunking button for unsupported files."]}, "date": "2024-11-04", "version": "1.26.20"}, {"children": {"fixes": ["Fix duplicate key value violates unique constraint \"slug_user_id_unique\" when create inbox session."]}, "date": "2024-11-03", "version": "1.26.19"}, {"children": {"fixes": ["Fix MS Entra ID and Azure AD authorization."]}, "date": "2024-11-03", "version": "1.26.18"}, {"children": {"improvements": ["Improve server log on chat api."]}, "date": "2024-10-31", "version": "1.26.17"}, {"children": {"fixes": ["Fix server Network connection lost error."]}, "date": "2024-10-31", "version": "1.26.16"}, {"children": {"improvements": ["Refactor embedding as plain vector array."]}, "date": "2024-10-31", "version": "1.26.15"}, {"children": {"improvements": ["Add fa-ir locale."]}, "date": "2024-10-30", "version": "1.26.14"}, {"children": {"fixes": ["Fix the artifacts interface not scrolling."]}, "date": "2024-10-30", "version": "1.26.13"}, {"children": {"fixes": ["Fix file image prompts in client mode."]}, "date": "2024-10-30", "version": "1.26.12"}, {"children": {}, "date": "2024-10-29", "version": "1.26.11"}, {"children": {"improvements": ["Refactor the aiChat slice actions."]}, "date": "2024-10-29", "version": "1.26.10"}, {"children": {}, "date": "2024-10-29", "version": "1.26.9"}, {"children": {"fixes": ["Update zhipu param process."]}, "date": "2024-10-29", "version": "1.26.8"}, {"children": {"fixes": ["Remove PWA Install in Firefox and Arc."]}, "date": "2024-10-29", "version": "1.26.7"}, {"children": {"improvements": ["Add Qwen2.5-72B-Instruct model on HF provider."]}, "date": "2024-10-29", "version": "1.26.6"}, {"children": {"improvements": ["Improve user guide when user not login."]}, "date": "2024-10-29", "version": "1.26.5"}, {"children": {"fixes": ["Remove the 'resetConversation' hot key."]}, "date": "2024-10-28", "version": "1.26.4"}, {"children": {"fixes": ["Fix Huggingface API interrupting when the output exceeds 140 tokens."], "improvements": ["Remove SenseChat-Vision model, due to model limitation."]}, "date": "2024-10-28", "version": "1.26.3"}, {"children": {"fixes": ["Fix page not switch when clicking on the pin assistant."]}, "date": "2024-10-28", "version": "1.26.2"}, {"children": {"improvements": ["Refactor the plugin prompts to xml format."]}, "date": "2024-10-27", "version": "1.26.1"}, {"children": {"features": ["experimentally support to pin assistant to sidebar."]}, "date": "2024-10-27", "version": "1.26.0"}, {"children": {"fixes": ["Fix the issue of the switch assistant portal not closing."]}, "date": "2024-10-27", "version": "1.25.3"}, {"children": {"improvements": ["Update stepfun models."]}, "date": "2024-10-27", "version": "1.25.2"}, {"children": {"fixes": ["Fix modelList merge."]}, "date": "2024-10-26", "version": "1.25.1"}, {"children": {"features": ["Support ZEN mode."]}, "date": "2024-10-26", "version": "1.25.0"}, {"children": {"improvements": ["Update Google Model list, add gemini-1.5-flash-8b."]}, "date": "2024-10-26", "version": "1.24.2"}, {"children": {"improvements": ["Refactor the Google Gen AI."]}, "date": "2024-10-25", "version": "1.24.1"}, {"children": {"features": ["Add SenseNova (商汤) model provider."]}, "date": "2024-10-25", "version": "1.24.0"}, {"children": {"improvements": ["Add *_MODEL_LIST env to all models, update Spark model id & display name."]}, "date": "2024-10-25", "version": "1.23.1"}, {"children": {"features": ["Support system agent config."]}, "date": "2024-10-25", "version": "1.23.0"}, {"children": {"improvements": ["Add bedrock claude-3.5-sonnect-v2."]}, "date": "2024-10-25", "version": "1.22.27"}, {"children": {"improvements": ["Fix some custom branding detail."]}, "date": "2024-10-23", "version": "1.22.26"}, {"children": {"improvements": ["Remove unused user tables."]}, "date": "2024-10-23", "version": "1.22.25"}, {"children": {"improvements": ["Support plugin flag."]}, "date": "2024-10-23", "version": "1.22.24"}, {"children": {"improvements": ["Improve error i18n."]}, "date": "2024-10-23", "version": "1.22.23"}, {"children": {"improvements": ["Improve i18n."]}, "date": "2024-10-23", "version": "1.22.22"}, {"children": {"improvements": ["Refactor cookie/headers to async mode."]}, "date": "2024-10-23", "version": "1.22.21"}, {"children": {"improvements": ["Add new claude-3.5-sonnet model."]}, "date": "2024-10-23", "version": "1.22.20"}, {"children": {"improvements": ["Move responsive to server utils folder."]}, "date": "2024-10-22", "version": "1.22.19"}, {"children": {}, "date": "2024-10-22", "version": "1.22.18"}, {"children": {"improvements": ["Fix dynamic import in rsc layout."]}, "date": "2024-10-22", "version": "1.22.17"}, {"children": {"fixes": ["Fix azure-ad."]}, "date": "2024-10-21", "version": "1.22.16"}, {"children": {"improvements": ["Update format utils and shared layout."]}, "date": "2024-10-21", "version": "1.22.15"}, {"children": {"improvements": ["Update wenxin 4.0 turbo model to latest."]}, "date": "2024-10-20", "version": "1.22.14"}, {"children": {"improvements": ["Add Ministral model, update Together AI model list, add function call & vision."]}, "date": "2024-10-20", "version": "1.22.13"}, {"children": {"improvements": ["Add Llama 3.1 Nemotron 70B model & reorder some provider model list."]}, "date": "2024-10-20", "version": "1.22.12"}, {"children": {"improvements": ["Refactor azure ad to ms entra id."]}, "date": "2024-10-20", "version": "1.22.11"}, {"children": {}, "date": "2024-10-20", "version": "1.22.10"}, {"children": {"improvements": ["Update Fireworks AI model list."]}, "date": "2024-10-18", "version": "1.22.9"}, {"children": {"improvements": ["Add Yi-Lightning model."]}, "date": "2024-10-17", "version": "1.22.8"}, {"children": {"improvements": ["Add qwen vision model & update qwen2.5 72b to 128k for siliconcloud."]}, "date": "2024-10-17", "version": "1.22.7"}, {"children": {"fixes": ["Fix images not go in to chat context."]}, "date": "2024-10-13", "version": "1.22.6"}, {"children": {"improvements": ["Reorder github model list & updata info & add new model."]}, "date": "2024-10-13", "version": "1.22.5"}, {"children": {"improvements": ["Separate message slice and aiChat slice."]}, "date": "2024-10-13", "version": "1.22.4"}, {"children": {"improvements": ["Support multi-windows for PWA."]}, "date": "2024-10-13", "version": "1.22.3"}, {"children": {"fixes": ["Allow use email as name in logto."]}, "date": "2024-10-13", "version": "1.22.2"}, {"children": {"fixes": ["Fix function calling issue, disable stream when using tools."]}, "date": "2024-10-12", "version": "1.22.1"}, {"children": {"improvements": ["Refactor the chat webapi."], "features": ["Add HuggingFace Model Provider."]}, "date": "2024-10-12", "version": "1.22.0"}, {"children": {}, "date": "2024-10-12", "version": "1.21.16"}, {"children": {}, "date": "2024-10-12", "version": "1.21.15"}, {"children": {"improvements": ["Fix artifacts render markdown."]}, "date": "2024-10-12", "version": "1.21.14"}, {"children": {"improvements": ["Refactor agent runtime implement of stream and ZHIPU provider."]}, "date": "2024-10-11", "version": "1.21.13"}, {"children": {"improvements": ["Refactor the jwt code."]}, "date": "2024-10-11", "version": "1.21.12"}, {"children": {"improvements": ["Refactor the backend code for better organization."]}, "date": "2024-10-11", "version": "1.21.11"}, {"children": {"improvements": ["Updata gpt-4o model info."]}, "date": "2024-10-11", "version": "1.21.10"}, {"children": {"improvements": ["Update qwen vl model to latest."]}, "date": "2024-10-10", "version": "1.21.9"}, {"children": {"fixes": ["Fix auto rewrite query when user message is too long."], "improvements": ["Support yml in file chunk."]}, "date": "2024-10-08", "version": "1.21.8"}, {"children": {"improvements": ["Refactor text-to-image endpoint."]}, "date": "2024-10-08", "version": "1.21.7"}, {"children": {"improvements": ["Move backend api to (backend) folder group."], "fixes": ["Fix txt-to-image api."]}, "date": "2024-10-05", "version": "1.21.6"}, {"children": {"improvements": ["Support shadcn in Artifacts."]}, "date": "2024-10-05", "version": "1.21.5"}, {"children": {"fixes": ["Fix recharts deps in the Artifacts React Renderer."]}, "date": "2024-10-02", "version": "1.21.4"}, {"children": {"improvements": ["Move most /api to /webapi."]}, "date": "2024-10-01", "version": "1.21.3"}, {"children": {"improvements": ["Adjust Wenxin icon size."]}, "date": "2024-10-01", "version": "1.21.2"}, {"children": {}, "date": "2024-09-30", "version": "1.21.1"}, {"children": {"features": ["Add wenxin model provider."]}, "date": "2024-09-30", "version": "1.21.0"}, {"children": {}, "date": "2024-09-30", "version": "1.20.8"}, {"children": {"improvements": ["Update groq model list."]}, "date": "2024-09-29", "version": "1.20.7"}, {"children": {}, "date": "2024-09-29", "version": "1.20.6"}, {"children": {}, "date": "2024-09-29", "version": "1.20.5"}, {"children": {}, "date": "2024-09-28", "version": "1.20.4"}, {"children": {"fixes": ["Improve delete orphan chunks when delete files."]}, "date": "2024-09-28", "version": "1.20.3"}, {"children": {"improvements": ["Add zhipu glm-4-flashx model."]}, "date": "2024-09-27", "version": "1.20.2"}, {"children": {}, "date": "2024-09-27", "version": "1.20.1"}, {"children": {"features": ["Add Hunyuan(Tencent) model provider."]}, "date": "2024-09-27", "version": "1.20.0"}, {"children": {"improvements": ["Add llama3.2 model for openrouter provider."]}, "date": "2024-09-27", "version": "1.19.36"}, {"children": {"improvements": ["Add o1-preview and o1-mini model to github model provider."]}, "date": "2024-09-27", "version": "1.19.35"}, {"children": {}, "date": "2024-09-26", "version": "1.19.34"}, {"children": {"fixes": ["MiniMax output long content interrupted by non-existent error."], "improvements": ["Update google provider model info."]}, "date": "2024-09-25", "version": "1.19.33"}, {"children": {"improvements": ["Add function call for taichu_llm."]}, "date": "2024-09-25", "version": "1.19.32"}, {"children": {"improvements": ["Add google gemini 1.5 002 series."]}, "date": "2024-09-24", "version": "1.19.31"}, {"children": {"improvements": ["Disable taichu2.0 functioncall & default disable taichu2.0v model."]}, "date": "2024-09-24", "version": "1.19.30"}, {"children": {"improvements": ["Update taichu provider info & add taichu vision model."]}, "date": "2024-09-24", "version": "1.19.29"}, {"children": {"improvements": ["Add function call support for Stepfun."]}, "date": "2024-09-24", "version": "1.19.28"}, {"children": {"improvements": ["Improve images display in chat messages."]}, "date": "2024-09-24", "version": "1.19.27"}, {"children": {"fixes": ["Fix url config import after user state init."], "improvements": ["Add support function call for 360AI, left sidebar has only assistants."]}, "date": "2024-09-24", "version": "1.19.26"}, {"children": {"fixes": ["Add missing translations."]}, "date": "2024-09-24", "version": "1.19.25"}, {"children": {"fixes": ["Fix artifacts code language highlight."]}, "date": "2024-09-23", "version": "1.19.24"}, {"children": {"improvements": ["Add spark max-32k model."]}, "date": "2024-09-23", "version": "1.19.23"}, {"children": {"fixes": ["Fix ollama model download panel."]}, "date": "2024-09-22", "version": "1.19.22"}, {"children": {"improvements": ["Refactor to improve branding customization."]}, "date": "2024-09-21", "version": "1.19.21"}, {"children": {"fixes": ["Fix Content-Security-Policy."]}, "date": "2024-09-21", "version": "1.19.20"}, {"children": {"fixes": ["Casdoor webhooks providerAccountId not found."]}, "date": "2024-09-21", "version": "1.19.19"}, {"children": {}, "date": "2024-09-21", "version": "1.19.18"}, {"children": {"fixes": ["providerAccountId not exist in provider."]}, "date": "2024-09-21", "version": "1.19.17"}, {"children": {"improvements": ["Improve i18n for discover and improve version check."]}, "date": "2024-09-21", "version": "1.19.16"}, {"children": {"improvements": ["Improve i18n in discover."]}, "date": "2024-09-20", "version": "1.19.15"}, {"children": {"fixes": ["<PERSON><PERSON> shiki@1.17.7 to fix code highlight."]}, "date": "2024-09-20", "version": "1.19.14"}, {"children": {"fixes": ["Try to implement better ssrf-protect."]}, "date": "2024-09-20", "version": "1.19.13"}, {"children": {"improvements": ["Support webhooks for casdoor."]}, "date": "2024-09-20", "version": "1.19.12"}, {"children": {"fixes": ["Custom model initialization not taking effect error."]}, "date": "2024-09-20", "version": "1.19.11"}, {"children": {"improvements": ["Add qwen2.5 math and coder model for siliconcloud provider."]}, "date": "2024-09-20", "version": "1.19.10"}, {"children": {"fixes": ["Fix a bug with server agent config when user not exist."]}, "date": "2024-09-20", "version": "1.19.9"}, {"children": {"improvements": ["Delete siliconflow outdated model & disable stepfun functioncall."]}, "date": "2024-09-19", "version": "1.19.8"}, {"children": {"improvements": ["Add siliconflow qwen2.5 model."]}, "date": "2024-09-19", "version": "1.19.7"}, {"children": {"improvements": ["Refactor the tts route url."]}, "date": "2024-09-19", "version": "1.19.6"}, {"children": {"improvements": ["Enable functioncall for stepfun models, Update qwen models."]}, "date": "2024-09-19", "version": "1.19.5"}, {"children": {"improvements": ["Refactor the sitemap implement."]}, "date": "2024-09-19", "version": "1.19.4"}, {"children": {}, "date": "2024-09-19", "version": "1.19.3"}, {"children": {"improvements": ["Updata qwen model info & add qwen2.5 & reorder provider list."]}, "date": "2024-09-19", "version": "1.19.2"}, {"children": {"improvements": ["Add mistral provider new models."]}, "date": "2024-09-19", "version": "1.19.1"}, {"children": {"features": ["Add Ai21Labs model provider, add Github Models provider, support native Artifacts just like <PERSON>."]}, "date": "2024-09-18", "version": "1.19.0"}, {"children": {"fixes": ["Fix InterceptingRoutes in discover."]}, "date": "2024-09-18", "version": "1.18.2"}, {"children": {}, "date": "2024-09-18", "version": "1.18.1"}, {"children": {"features": ["Add Discover Page."]}, "date": "2024-09-18", "version": "1.18.0"}, {"children": {"fixes": ["Fix a corner case of tools_call with empty object."], "improvements": ["Delete duplicate models in ollama."]}, "date": "2024-09-16", "version": "1.17.7"}, {"children": {"improvements": ["Rename artifacts to plugins in portal."]}, "date": "2024-09-15", "version": "1.17.6"}, {"children": {"improvements": ["Add MiniCPM-V 8B model entries to Ollama model providers."]}, "date": "2024-09-15", "version": "1.17.5"}, {"children": {"improvements": ["Update fullscreen loading style."]}, "date": "2024-09-15", "version": "1.17.4"}, {"children": {"improvements": ["Delete \"-\" in deepseek displayname."]}, "date": "2024-09-14", "version": "1.17.3"}, {"children": {"fixes": ["Fix o1 model list."], "improvements": ["Update openrouter model list."]}, "date": "2024-09-13", "version": "1.17.2"}, {"children": {"improvements": ["Update zhipu model info."]}, "date": "2024-09-13", "version": "1.17.1"}, {"children": {"features": ["Support openai new OpenAI o1-preview/o1-mini models."], "improvements": ["Support Google Model List."]}, "date": "2024-09-13", "version": "1.17.0"}, {"children": {}, "date": "2024-09-13", "version": "1.16.14"}, {"children": {"improvements": ["Update siliconcloud model."]}, "date": "2024-09-13", "version": "1.16.13"}, {"children": {"improvements": ["Remove brackets from model names with dates in OpenAI."]}, "date": "2024-09-12", "version": "1.16.12"}, {"children": {"fixes": ["Support webhooks for logto."], "improvements": ["Default disable mistral provider useless models."]}, "date": "2024-09-12", "version": "1.16.11"}, {"children": {"improvements": ["Support Environment Variable Inference For NextAuth."], "fixes": ["<PERSON><PERSON> model param error."]}, "date": "2024-09-12", "version": "1.16.10"}, {"children": {"improvements": ["Add model and provider desc and url."]}, "date": "2024-09-12", "version": "1.16.9"}, {"children": {"improvements": ["Improve models and add more info for providers and models."]}, "date": "2024-09-12", "version": "1.16.8"}, {"children": {"improvements": ["Optimize model token display method."]}, "date": "2024-09-11", "version": "1.16.7"}, {"children": {"fixes": ["<PERSON>n next@14.2.8 to fix Internal error."]}, "date": "2024-09-11", "version": "1.16.6"}, {"children": {}, "date": "2024-09-11", "version": "1.16.5"}, {"children": {}, "date": "2024-09-11", "version": "1.16.4"}, {"children": {"fixes": ["Add LLM_VISION_IMAGE_USE_BASE64 to support local s3 in vision model."]}, "date": "2024-09-11", "version": "1.16.3"}, {"children": {"improvements": ["Update Upstage model list."]}, "date": "2024-09-11", "version": "1.16.2"}, {"children": {"improvements": ["Reorder the provider list, update spark check model to spark-lite & default disable useless model."]}, "date": "2024-09-10", "version": "1.16.1"}, {"children": {"features": ["Add Fireworks AI Model Provider, Add Spark model provider."]}, "date": "2024-09-10", "version": "1.16.0"}, {"children": {"improvements": ["Update CustomLogo."]}, "date": "2024-09-10", "version": "1.15.35"}, {"children": {"improvements": ["Change empty content stream behavior."]}, "date": "2024-09-10", "version": "1.15.34"}, {"children": {"fixes": ["Fix /etc/resolv.confedit permission in docker image."]}, "date": "2024-09-10", "version": "1.15.33"}, {"children": {"fixes": ["Fix tools calling in some edge cases."]}, "date": "2024-09-10", "version": "1.15.32"}, {"children": {"fixes": ["<PERSON><PERSON><PERSON> should not introduce freequency_penality parameters."]}, "date": "2024-09-10", "version": "1.15.31"}, {"children": {"fixes": ["Fix claude 3.5 image with s3 url."]}, "date": "2024-09-09", "version": "1.15.30"}, {"children": {"fixes": ["Gemini cannot input images when server database is enabled."]}, "date": "2024-09-09", "version": "1.15.29"}, {"children": {"fixes": ["Update baichuan param."]}, "date": "2024-09-09", "version": "1.15.28"}, {"children": {"improvements": ["Add siliconcloud new model."]}, "date": "2024-09-09", "version": "1.15.27"}, {"children": {"improvements": ["Update perplexity model list."]}, "date": "2024-09-09", "version": "1.15.26"}, {"children": {}, "date": "2024-09-09", "version": "1.15.25"}, {"children": {"improvements": ["Fix title in about settings."]}, "date": "2024-09-09", "version": "1.15.24"}, {"children": {"improvements": ["Improve branding implement."]}, "date": "2024-09-08", "version": "1.15.23"}, {"children": {"improvements": ["Update model display name & Remove Qwen preview model."]}, "date": "2024-09-08", "version": "1.15.22"}, {"children": {"improvements": ["Temperature range from 0 to 2."]}, "date": "2024-09-08", "version": "1.15.21"}, {"children": {}, "date": "2024-09-08", "version": "1.15.20"}, {"children": {}, "date": "2024-09-08", "version": "1.15.19"}, {"children": {"improvements": ["Support anthropic browser request."]}, "date": "2024-09-06", "version": "1.15.18"}, {"children": {"fixes": ["Fix auth log."]}, "date": "2024-09-06", "version": "1.15.17"}, {"children": {"improvements": ["Update Bedrock model list & add AWS_BEDROCK_MODEL_LIST support."]}, "date": "2024-09-06", "version": "1.15.16"}, {"children": {"improvements": ["Add LLaVA 1.5 7B model in Groq."]}, "date": "2024-09-06", "version": "1.15.15"}, {"children": {}, "date": "2024-09-06", "version": "1.15.14"}, {"children": {}, "date": "2024-09-06", "version": "1.15.13"}, {"children": {"fixes": ["Fix typo in RAG prompt."]}, "date": "2024-09-04", "version": "1.15.12"}, {"children": {}, "date": "2024-09-04", "version": "1.15.11"}, {"children": {}, "date": "2024-09-03", "version": "1.15.10"}, {"children": {"fixes": ["Fix speed and rag prompt."]}, "date": "2024-09-03", "version": "1.15.9"}, {"children": {"fixes": ["Fix .PDF can not be chunked."]}, "date": "2024-09-03", "version": "1.15.8"}, {"children": {"improvements": ["Fix provider disabled title style."]}, "date": "2024-09-03", "version": "1.15.7"}, {"children": {"improvements": ["Stepfun default enabled model, update Groq model list & add GROQ_MODEL_LIST support."]}, "date": "2024-09-01", "version": "1.15.6"}, {"children": {"improvements": ["Update Together AI model list."]}, "date": "2024-09-01", "version": "1.15.5"}, {"children": {"improvements": ["Update Novita AI model info & add NOVITA_MODEL_LIST support."]}, "date": "2024-09-01", "version": "1.15.4"}, {"children": {"improvements": ["Add *_MODEL_LIST for Qwen and ZeroOne, fix model info, update Claude 3.5 Sonnet maxOutput vaule."]}, "date": "2024-09-01", "version": "1.15.3"}, {"children": {"improvements": ["Update Qwen and Gemini models info."]}, "date": "2024-08-30", "version": "1.15.2"}, {"children": {"improvements": ["Update the sorting of each provider model."]}, "date": "2024-08-30", "version": "1.15.1"}, {"children": {"features": ["Add Upstage model provider support."]}, "date": "2024-08-30", "version": "1.15.0"}, {"children": {"improvements": ["Fix ms doc file preview, Update the sorting of each provider model."]}, "date": "2024-08-30", "version": "1.14.12"}, {"children": {"improvements": ["Update Stepfun models info."]}, "date": "2024-08-30", "version": "1.14.11"}, {"children": {"fixes": ["Fix file relative chunks."]}, "date": "2024-08-30", "version": "1.14.10"}, {"children": {}, "date": "2024-08-29", "version": "1.14.9"}, {"children": {"fixes": ["Fix whisper-1 typo."]}, "date": "2024-08-29", "version": "1.14.8"}, {"children": {"fixes": ["Disable ChatGPT-4o Tools Calling."], "improvements": ["Improve chunk and file preview."]}, "date": "2024-08-28", "version": "1.14.7"}, {"children": {"improvements": ["Update Gemini models."]}, "date": "2024-08-28", "version": "1.14.6"}, {"children": {"fixes": ["No user name if Cloudflare Zero Trust with onetimepin."]}, "date": "2024-08-28", "version": "1.14.5"}, {"children": {"improvements": ["Move model and provider icon components to @lobehub/icons."]}, "date": "2024-08-28", "version": "1.14.4"}, {"children": {"fixes": ["Improve aysnc error type."]}, "date": "2024-08-27", "version": "1.14.3"}, {"children": {"fixes": ["Fix agent setting."]}, "date": "2024-08-27", "version": "1.14.2"}, {"children": {"improvements": ["Improve zhipu model config."]}, "date": "2024-08-27", "version": "1.14.1"}, {"children": {"features": ["Supports Cloudflare Zero Trust login."]}, "date": "2024-08-27", "version": "1.14.0"}, {"children": {"fixes": ["Bypass vercel deployment protection, fix can send message on uploading files."]}, "date": "2024-08-27", "version": "1.13.2"}, {"children": {"improvements": ["Update Qwen models."]}, "date": "2024-08-27", "version": "1.13.1"}, {"children": {"features": ["Supports <PERSON><PERSON><PERSON> login."]}, "date": "2024-08-27", "version": "1.13.0"}, {"children": {"fixes": ["Feature flag knowledge_base doesn't affect ActionBar."]}, "date": "2024-08-26", "version": "1.12.20"}, {"children": {"fixes": ["Fix cannot clone agent when imported from client."]}, "date": "2024-08-25", "version": "1.12.19"}, {"children": {"fixes": ["Fix dayjs error in en-US language."]}, "date": "2024-08-25", "version": "1.12.18"}, {"children": {"fixes": ["Fix multi file upload dupicate."]}, "date": "2024-08-25", "version": "1.12.17"}, {"children": {"fixes": ["Session not found error on mobile."]}, "date": "2024-08-24", "version": "1.12.16"}, {"children": {}, "date": "2024-08-24", "version": "1.12.15"}, {"children": {"fixes": ["Fix tts file saving in server mode."]}, "date": "2024-08-24", "version": "1.12.14"}, {"children": {"improvements": ["Update 01.AI models."]}, "date": "2024-08-24", "version": "1.12.13"}, {"children": {}, "date": "2024-08-24", "version": "1.12.12"}, {"children": {"fixes": ["Remove orphan chunks if there is no related file."]}, "date": "2024-08-23", "version": "1.12.11"}, {"children": {"fixes": ["Refactor and fix dalle."]}, "date": "2024-08-23", "version": "1.12.10"}, {"children": {"fixes": ["Improve s3 path-style url."]}, "date": "2024-08-23", "version": "1.12.9"}, {"children": {"fixes": ["Fix NEXT_PUBLIC_S3_DOMAIN error on Docker."]}, "date": "2024-08-22", "version": "1.12.8"}, {"children": {"fixes": ["Logout button not shown on mobile view when using nextauth."]}, "date": "2024-08-22", "version": "1.12.7"}, {"children": {"improvements": ["Refactor s3 env and support path-style for minio."]}, "date": "2024-08-22", "version": "1.12.6"}, {"children": {"fixes": ["Fix clipboard copy issue and improve upload cors feedback."]}, "date": "2024-08-22", "version": "1.12.5"}, {"children": {"improvements": ["Fix link style."]}, "date": "2024-08-22", "version": "1.12.4"}, {"children": {"improvements": ["Hide settings in repo."]}, "date": "2024-08-22", "version": "1.12.3"}, {"children": {}, "date": "2024-08-22", "version": "1.12.2"}, {"children": {"fixes": ["Fix embeddings multi-insert when there is issues with async task."]}, "date": "2024-08-21", "version": "1.12.1"}, {"children": {"features": ["Files and knowledge base."]}, "date": "2024-08-21", "version": "1.12.0"}, {"children": {"fixes": ["Fixed bedrock llama model id."]}, "date": "2024-08-19", "version": "1.11.9"}, {"children": {"improvements": ["Update zhipu models."]}, "date": "2024-08-19", "version": "1.11.8"}, {"children": {"fixes": ["Fix topic scroll issue."]}, "date": "2024-08-18", "version": "1.11.7"}, {"children": {"improvements": ["Refactor the SITE_URL to APP_URL."]}, "date": "2024-08-18", "version": "1.11.6"}, {"children": {"improvements": ["Refactor the fetch method to fix response.undefined."]}, "date": "2024-08-18", "version": "1.11.5"}, {"children": {"improvements": ["Add SILICONCLOUD_MODEL_LIST & SILICONCLOUD_PROXY_URL support for SiliconCloud."]}, "date": "2024-08-18", "version": "1.11.4"}, {"children": {"improvements": ["Refactor PanelTitle and move commit from file uploading."]}, "date": "2024-08-17", "version": "1.11.3"}, {"children": {}, "date": "2024-08-17", "version": "1.11.2"}, {"children": {"fixes": ["Make S3 upload ACL setting optional."]}, "date": "2024-08-15", "version": "1.11.1"}, {"children": {"features": ["Add 2 new models to openai provider."]}, "date": "2024-08-14", "version": "1.11.0"}, {"children": {}, "date": "2024-08-14", "version": "1.10.1"}, {"children": {"features": ["Add SiliconCloud model provider."]}, "date": "2024-08-14", "version": "1.10.0"}, {"children": {"improvements": ["Resize the image size in chat message."]}, "date": "2024-08-13", "version": "1.9.8"}, {"children": {}, "date": "2024-08-13", "version": "1.9.7"}, {"children": {}, "date": "2024-08-09", "version": "1.9.6"}, {"children": {"improvements": ["Updated AWS bedrock model list."]}, "date": "2024-08-08", "version": "1.9.5"}, {"children": {"fixes": ["Fix import clerk <PERSON><PERSON><PERSON><PERSON> from public api."]}, "date": "2024-08-06", "version": "1.9.4"}, {"children": {"improvements": ["Refactor server db schema for better code organize."]}, "date": "2024-08-06", "version": "1.9.3"}, {"children": {}, "date": "2024-08-05", "version": "1.9.2"}, {"children": {"fixes": ["Azure modelTag icon display."]}, "date": "2024-08-05", "version": "1.9.1"}, {"children": {"features": ["Skip login page if only one provider exists."]}, "date": "2024-08-05", "version": "1.9.0"}, {"children": {"fixes": ["Add PROXY_URL in docker with proxychains-ng."]}, "date": "2024-08-03", "version": "1.8.2"}, {"children": {"improvements": ["Fix aya, mathstral model tag icon & update ollama model info."]}, "date": "2024-08-03", "version": "1.8.1"}, {"children": {"features": ["Add NextAuth as authentication service in server database."]}, "date": "2024-08-02", "version": "1.8.0"}, {"children": {"improvements": ["Add Gemini 1.5 Pro Exp model."]}, "date": "2024-08-02", "version": "1.7.10"}, {"children": {"fixes": ["Fix Mistral models calling & update model info."], "improvements": ["Fix stepfun & baichuan model tag icon missing, update Perplexity models."]}, "date": "2024-08-01", "version": "1.7.9"}, {"children": {}, "date": "2024-07-30", "version": "1.7.8"}, {"children": {"improvements": ["Improve tools calling UI."]}, "date": "2024-07-30", "version": "1.7.7"}, {"children": {"fixes": ["Disable anthropic browser request."]}, "date": "2024-07-29", "version": "1.7.6"}, {"children": {"fixes": ["Fix create_session  edit_agent feature flags and add more flags."], "improvements": ["Update 360GPT model (360GPT2 Pro)."]}, "date": "2024-07-29", "version": "1.7.5"}, {"children": {"fixes": ["Improve remote model list fetching for Novita AI."]}, "date": "2024-07-29", "version": "1.7.4"}, {"children": {"fixes": ["Update minimax models."]}, "date": "2024-07-28", "version": "1.7.3"}, {"children": {"fixes": ["Avoid baseURL being an empty string, resulting in incorrect client fetch."]}, "date": "2024-07-26", "version": "1.7.2"}, {"children": {"fixes": ["Fix dalle tools calling prompts to avoid content risk."]}, "date": "2024-07-26", "version": "1.7.1"}, {"children": {"features": ["Enabled function calling on Deepseek models."]}, "date": "2024-07-26", "version": "1.7.0"}, {"children": {"improvements": ["Fix file upload height."]}, "date": "2024-07-26", "version": "1.6.15"}, {"children": {"improvements": ["Improve input file upload."]}, "date": "2024-07-26", "version": "1.6.14"}, {"children": {"improvements": ["Updated Groq model list to include llama-3.1 and llama3-Groq."]}, "date": "2024-07-25", "version": "1.6.13"}, {"children": {"improvements": ["Add new models to groq which are llama 3.1."]}, "date": "2024-07-25", "version": "1.6.12"}, {"children": {"fixes": ["Fix UNAUTHORIZED issue with clerk auth provider."]}, "date": "2024-07-24", "version": "1.6.11"}, {"children": {"improvements": ["Fix the scrolling of the return result area of function calling."]}, "date": "2024-07-23", "version": "1.6.10"}, {"children": {}, "date": "2024-07-23", "version": "1.6.9"}, {"children": {"improvements": ["Move server modules."]}, "date": "2024-07-23", "version": "1.6.8"}, {"children": {"improvements": ["Add new model provider Novita AI."]}, "date": "2024-07-23", "version": "1.6.7"}, {"children": {}, "date": "2024-07-22", "version": "1.6.6"}, {"children": {"fixes": ["Content lost unexpectedly on Qwen provider when finish_reason is stop."]}, "date": "2024-07-22", "version": "1.6.5"}, {"children": {"improvements": ["Add trpc query client with react-query."]}, "date": "2024-07-21", "version": "1.6.4"}, {"children": {"improvements": ["Update Zhipu models (GLM-4-AllTools & CodeGeeX-4)."]}, "date": "2024-07-21", "version": "1.6.3"}, {"children": {"fixes": ["Fix dayjs render on server."]}, "date": "2024-07-21", "version": "1.6.2"}, {"children": {"improvements": ["Refactor the DragUpload."]}, "date": "2024-07-19", "version": "1.6.1"}, {"children": {"features": ["Add gpt-4o-mini in OpenAI Provider and set it as the default model."]}, "date": "2024-07-19", "version": "1.6.0"}, {"children": {"improvements": ["Added Gemma2 instead of outdated <PERSON>."]}, "date": "2024-07-19", "version": "1.5.5"}, {"children": {"fixes": ["Fix delete session group."]}, "date": "2024-07-17", "version": "1.5.4"}, {"children": {"fixes": ["Fix OpenAI deployment restrictions, fix cant duplicate assistant."]}, "date": "2024-07-17", "version": "1.5.3"}, {"children": {"fixes": ["Fix session not reorder after send message."]}, "date": "2024-07-17", "version": "1.5.2"}, {"children": {"improvements": ["Improve brand url."]}, "date": "2024-07-17", "version": "1.5.1"}, {"children": {"features": ["Spport qwen-vl and tool call for qwen."]}, "date": "2024-07-17", "version": "1.5.0"}, {"children": {"fixes": ["Save assistant info on blur."]}, "date": "2024-07-15", "version": "1.4.3"}, {"children": {"improvements": ["Remove code related to gemini-pro-vision."]}, "date": "2024-07-13", "version": "1.4.2"}, {"children": {"improvements": ["Add cloud promotion banner."]}, "date": "2024-07-13", "version": "1.4.1"}, {"children": {"features": ["Add 360AI model provider."]}, "date": "2024-07-12", "version": "1.4.0"}, {"children": {"improvements": ["Improve agent runtime code."]}, "date": "2024-07-11", "version": "1.3.6"}, {"children": {"fixes": ["Fix assistant meta change race issue."]}, "date": "2024-07-10", "version": "1.3.5"}, {"children": {"improvements": ["Support disable clientFetch by default."]}, "date": "2024-07-09", "version": "1.3.4"}, {"children": {"fixes": ["Allow user to use their own WebRTC signaling."]}, "date": "2024-07-09", "version": "1.3.3"}, {"children": {"fixes": ["Automatic refresh when change language."]}, "date": "2024-07-09", "version": "1.3.2"}, {"children": {}, "date": "2024-07-09", "version": "1.3.1"}, {"children": {"features": ["Add Taichu model provider."]}, "date": "2024-07-09", "version": "1.3.0"}, {"children": {"improvements": ["Provider changes with model in model settings."]}, "date": "2024-07-08", "version": "1.2.14"}, {"children": {"fixes": ["Fix tool message order."]}, "date": "2024-07-07", "version": "1.2.13"}, {"children": {"fixes": ["Fixed mobile web page navigation issue with inbox assistant, support to disable clerk signup with feature flag."]}, "date": "2024-07-07", "version": "1.2.12"}, {"children": {"improvements": ["Update deepseek max token."]}, "date": "2024-07-07", "version": "1.2.11"}, {"children": {"fixes": ["Improve tools calling error feedback when arguments are not correctly."]}, "date": "2024-07-05", "version": "1.2.10"}, {"children": {"improvements": ["Fix tool message suspense loading."]}, "date": "2024-07-04", "version": "1.2.9"}, {"children": {"fixes": ["Allow builtin tools to trigger AI message."]}, "date": "2024-07-03", "version": "1.2.8"}, {"children": {"improvements": ["Improve delete assistant message with tools."]}, "date": "2024-07-03", "version": "1.2.7"}, {"children": {"fixes": ["Clerk provider refreshes continously."]}, "date": "2024-07-03", "version": "1.2.6"}, {"children": {"fixes": ["Fix clerk appearance is not applied correctly."]}, "date": "2024-07-02", "version": "1.2.5"}, {"children": {"improvements": ["Update ProviderAvatar for Baichuan & Stepfun."]}, "date": "2024-07-02", "version": "1.2.4"}, {"children": {"improvements": ["Refactor the portal implement."]}, "date": "2024-07-01", "version": "1.2.3"}, {"children": {"fixes": ["Display issue when select default model in System Agent."]}, "date": "2024-07-01", "version": "1.2.2"}, {"children": {"improvements": ["Refactor the portal file."]}, "date": "2024-07-01", "version": "1.2.1"}, {"children": {"features": ["Add Baichuan model provider."]}, "date": "2024-07-01", "version": "1.2.0"}, {"children": {"improvements": ["Add Gemini 1.5 stable version model & 2M context window."]}, "date": "2024-07-01", "version": "1.1.18"}, {"children": {"improvements": ["Refactor to prepare for Chat Portal."]}, "date": "2024-07-01", "version": "1.1.17"}, {"children": {"fixes": ["Fix clerk <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> error after long-time hang-up."]}, "date": "2024-06-29", "version": "1.1.16"}, {"children": {"fixes": ["Pin @azure/core-rest-pipeline@1.16.0 to fix azure openai issue."]}, "date": "2024-06-28", "version": "1.1.15"}, {"children": {"improvements": ["Refactor plugin state."]}, "date": "2024-06-27", "version": "1.1.14"}, {"children": {"fixes": ["-check_updates cannot be set by FEATURE_FLAGS."]}, "date": "2024-06-27", "version": "1.1.13"}, {"children": {"fixes": ["Fix azure tools calling."]}, "date": "2024-06-26", "version": "1.1.12"}, {"children": {"improvements": ["Refactor format utils."]}, "date": "2024-06-25", "version": "1.1.11"}, {"children": {}, "date": "2024-06-24", "version": "1.1.10"}, {"children": {"fixes": ["Fix agent tags."], "improvements": ["Always show action on mobile."]}, "date": "2024-06-24", "version": "1.1.9"}, {"children": {"fixes": ["Fix anthropic parallel tools calling."]}, "date": "2024-06-24", "version": "1.1.8"}, {"children": {"improvements": ["Add AES-GCM footer and tooltip."]}, "date": "2024-06-24", "version": "1.1.7"}, {"children": {"improvements": ["Refactor the server db implement."], "fixes": ["Fix incorrect baseURL for Groq in client mode."]}, "date": "2024-06-23", "version": "1.1.6"}, {"children": {"improvements": ["Remove deprecated env."]}, "date": "2024-06-23", "version": "1.1.5"}, {"children": {"fixes": ["Create first-time user on server db."]}, "date": "2024-06-22", "version": "1.1.4"}, {"children": {"fixes": ["Ollama not enabled client fetch by default."]}, "date": "2024-06-21", "version": "1.1.3"}, {"children": {}, "date": "2024-06-20", "version": "1.1.2"}, {"children": {"improvements": ["Fixed System Agent missing in mobile layout."]}, "date": "2024-06-20", "version": "1.1.1"}, {"children": {"features": ["<PERSON><PERSON><PERSON> Claude 3.5 Sonnet."]}, "date": "2024-06-20", "version": "1.1.0"}, {"children": {"fixes": ["Fix to send image without text."]}, "date": "2024-06-20", "version": "1.0.14"}, {"children": {"fixes": ["Fix and improve tool calling."]}, "date": "2024-06-19", "version": "1.0.13"}, {"children": {"fixes": ["Fix auto avatar."]}, "date": "2024-06-19", "version": "1.0.12"}, {"children": {"improvements": ["Fix phi3 icon display under OpenRouter."]}, "date": "2024-06-19", "version": "1.0.11"}, {"children": {"fixes": ["<PERSON><PERSON> in reset settings."]}, "date": "2024-06-19", "version": "1.0.10"}, {"children": {}, "date": "2024-06-19", "version": "1.0.9"}, {"children": {"fixes": ["Fix hydration mismatch on macOS."], "improvements": ["Update brand and improve docs."]}, "date": "2024-06-18", "version": "1.0.8"}, {"children": {"improvements": ["Remove some i18n text."]}, "date": "2024-06-18", "version": "1.0.7"}, {"children": {"improvements": ["Fix footer being cut on wide screen."]}, "date": "2024-06-17", "version": "1.0.6"}, {"children": {"fixes": ["Fix onboard and auth for community version."]}, "date": "2024-06-17", "version": "1.0.5"}, {"children": {"improvements": ["Add stepfun as a new provider."]}, "date": "2024-06-17", "version": "1.0.4"}, {"children": {"fixes": ["Fix clerk UNAUTHORIZED auth error."]}, "date": "2024-06-17", "version": "1.0.3"}, {"children": {"fixes": ["Openai key and openai proxy are invalid in feature flags."]}, "date": "2024-06-17", "version": "1.0.2"}, {"children": {"improvements": ["Remove r2 cdn url."]}, "date": "2024-06-17", "version": "1.0.1"}, {"children": {"features": ["Release LobeChat 1.0."], "improvements": ["Bump version.", "Release LobeChat 1.0, closes #2897 (97bb377)", "Bump version, closes #2896 (6e2d03b)", "Update LICENSE to Apache 2.0", "update to 1.0.0"]}, "date": "2024-06-17", "version": "1.0.0"}]