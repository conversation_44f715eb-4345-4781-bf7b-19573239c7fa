name: Release CI
on:
  push:
    branches:
      - main

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest

    services:
      postgres:
        image: pgvector/pgvector:pg16
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready --health-interval 10s --health-timeout 5s  --health-retries 5
        ports:
          - 5432:5432

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ secrets.BUN_VERSION }}

      - name: Install deps
        run: bun i

      - name: Lint
        run: bun run lint

      - name: Test Server Coverage
        run: bun run test-server:coverage
        env:
          DATABASE_TEST_URL: postgresql://postgres:postgres@localhost:5432/postgres
          DATABASE_DRIVER: node
          NEXT_PUBLIC_SERVICE_MODE: server
          KEY_VAULTS_SECRET: LA7n9k3JdEcbSgml2sxfw+4TV1AzaaFU5+R176aQz4s=
          S3_PUBLIC_DOMAIN: https://example.com
          APP_URL: https://home.com

      - name: Test App Coverage
        run: bun run test-app:coverage

      - name: Release
        run: bun run release
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Workflow
        run: bun run workflow:readme

      - name: Commit changes
        run: |-
          git diff
          git config --global user.name "lobehubbot"
          git config --global user.email "<EMAIL>"
          git add .
          git commit -m "📝 docs(bot): Auto sync agents & plugin to readme" || exit 0
          git push
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
