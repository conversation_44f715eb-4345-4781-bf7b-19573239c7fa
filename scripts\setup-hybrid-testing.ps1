# ============================================================================
# Neupria Hybrid Testing Setup Script
# ============================================================================
# This script sets up local environment with cloud AI API integration

param(
    [switch]$SkipAPIKeyValidation,
    [switch]$QuickStart,
    [string]$ConfigFile = "docker-compose/local/.env"
)

Write-Host "🚀 Setting up Neupria Hybrid Testing Environment" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check prerequisites
function Test-Prerequisites {
    Write-Host "📋 Checking prerequisites..." -ForegroundColor Yellow
    
    # Check Docker
    try {
        docker --version | Out-Null
        Write-Host "✅ Docker is installed" -ForegroundColor Green
    } catch {
        Write-Host "❌ Docker is not installed. Please install Docker Desktop." -ForegroundColor Red
        exit 1
    }
    
    # Check Docker Compose
    try {
        docker-compose --version | Out-Null
        Write-Host "✅ Docker Compose is installed" -ForegroundColor Green
    } catch {
        Write-Host "❌ Docker Compose is not installed." -ForegroundColor Red
        exit 1
    }
    
    # Check Node.js
    try {
        node --version | Out-Null
        Write-Host "✅ Node.js is installed" -ForegroundColor Green
    } catch {
        Write-Host "❌ Node.js is not installed. Please install Node.js 18+." -ForegroundColor Red
        exit 1
    }
}

# Setup environment file
function Setup-Environment {
    Write-Host "🔧 Setting up environment configuration..." -ForegroundColor Yellow
    
    $envExample = "docker-compose/local/.env.example"
    $envFile = $ConfigFile
    
    if (!(Test-Path $envExample)) {
        Write-Host "❌ .env.example file not found at $envExample" -ForegroundColor Red
        exit 1
    }
    
    if (!(Test-Path $envFile)) {
        Write-Host "📝 Creating .env file from .env.example..." -ForegroundColor Blue
        Copy-Item $envExample $envFile
        Write-Host "✅ Created $envFile" -ForegroundColor Green
    } else {
        Write-Host "⚠️  .env file already exists at $envFile" -ForegroundColor Yellow
        $overwrite = Read-Host "Do you want to overwrite it? (y/N)"
        if ($overwrite -eq "y" -or $overwrite -eq "Y") {
            Copy-Item $envExample $envFile -Force
            Write-Host "✅ Overwritten $envFile" -ForegroundColor Green
        }
    }
}

# Validate API keys
function Test-APIKeys {
    if ($SkipAPIKeyValidation) {
        Write-Host "⏭️  Skipping API key validation" -ForegroundColor Yellow
        return
    }
    
    Write-Host "🔑 Validating API keys..." -ForegroundColor Yellow
    
    # Load environment variables
    $envContent = Get-Content $ConfigFile
    $envVars = @{}
    foreach ($line in $envContent) {
        if ($line -match "^([^#][^=]+)=(.*)$") {
            $envVars[$matches[1]] = $matches[2]
        }
    }
    
    # Check Azure OpenAI
    if ($envVars["AZURE_API_KEY"] -and $envVars["AZURE_API_KEY"] -ne "your_azure_openai_key_here") {
        Write-Host "✅ Azure OpenAI API key configured" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Azure OpenAI API key not configured" -ForegroundColor Yellow
    }
    
    # Check AWS Bedrock
    if ($envVars["AWS_ACCESS_KEY_ID"] -and $envVars["AWS_ACCESS_KEY_ID"] -ne "your_aws_access_key_here") {
        Write-Host "✅ AWS Bedrock credentials configured" -ForegroundColor Green
    } else {
        Write-Host "⚠️  AWS Bedrock credentials not configured" -ForegroundColor Yellow
    }
    
    # Check GCP Vertex AI
    if ($envVars["GOOGLE_API_KEY"] -and $envVars["GOOGLE_API_KEY"] -ne "your_vertex_ai_key_here") {
        Write-Host "✅ GCP Vertex AI API key configured" -ForegroundColor Green
    } else {
        Write-Host "⚠️  GCP Vertex AI API key not configured" -ForegroundColor Yellow
    }
}

# Start services
function Start-Services {
    Write-Host "🐳 Starting Docker services..." -ForegroundColor Yellow
    
    Set-Location "docker-compose/local"
    
    # Pull latest images
    Write-Host "📥 Pulling Docker images..." -ForegroundColor Blue
    docker-compose pull
    
    # Start services
    Write-Host "🚀 Starting services..." -ForegroundColor Blue
    docker-compose up -d
    
    # Wait for services to be ready
    Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Blue
    Start-Sleep -Seconds 30
    
    # Check service status
    Write-Host "📊 Service status:" -ForegroundColor Blue
    docker-compose ps
    
    Set-Location "../.."
}

# Display access information
function Show-AccessInfo {
    Write-Host ""
    Write-Host "🎉 Hybrid Testing Environment Ready!" -ForegroundColor Green
    Write-Host "====================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Access URLs:" -ForegroundColor Cyan
    Write-Host "   • LobeChat App:    http://localhost:3210" -ForegroundColor White
    Write-Host "   • MinIO Console:   http://localhost:9001" -ForegroundColor White
    Write-Host "   • Casdoor Auth:    http://localhost:8000" -ForegroundColor White
    Write-Host "   • PostgreSQL:      localhost:5432" -ForegroundColor White
    Write-Host ""
    Write-Host "🔑 Default Credentials:" -ForegroundColor Cyan
    Write-Host "   • MinIO: admin / YOUR_MINIO_PASSWORD" -ForegroundColor White
    Write-Host "   • PostgreSQL: postgres / uWNZugjBqixf8dxC" -ForegroundColor White
    Write-Host ""
    Write-Host "🧪 Testing Commands:" -ForegroundColor Cyan
    Write-Host "   • Run tests:       npm run test" -ForegroundColor White
    Write-Host "   • AI model tests:  npm run test:ai" -ForegroundColor White
    Write-Host "   • Integration:     npm run test:integration" -ForegroundColor White
    Write-Host ""
    Write-Host "📝 Next Steps:" -ForegroundColor Cyan
    Write-Host "   1. Configure your API keys in: $ConfigFile" -ForegroundColor White
    Write-Host "   2. Run: npm install" -ForegroundColor White
    Write-Host "   3. Run: npm run dev" -ForegroundColor White
    Write-Host "   4. Open: http://localhost:3210" -ForegroundColor White
}

# Main execution
function Main {
    try {
        Test-Prerequisites
        Setup-Environment
        
        if (!$QuickStart) {
            Test-APIKeys
        }
        
        Start-Services
        Show-AccessInfo
        
        Write-Host ""
        Write-Host "✅ Setup completed successfully!" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Setup failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Run main function
Main
