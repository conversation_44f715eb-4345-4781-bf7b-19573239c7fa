name: Desktop PR Build

on:
  pull_request:
    types: [synchronize, labeled, unlabeled] # PR 更新或标签变化时触发

# 确保同一时间只运行一个相同的 workflow，取消正在进行的旧的运行
concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

# Add default permissions
permissions: read-all

env:
  PR_TAG_PREFIX: pr- # PR 构建版本的前缀标识

jobs:
  test:
    name: Code quality check
    # 添加 PR label 触发条件，只有添加了 Build Desktop 标签的 PR 才会触发构建
    if: contains(github.event.pull_request.labels.*.name, 'Build Desktop')
    runs-on: ubuntu-latest # 只在 ubuntu 上运行一次检查
    steps:
      - name: Checkout base
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9

      - name: Install deps
        run: pnpm install
        env:
          NODE_OPTIONS: --max-old-space-size=6144

      - name: Lint
        run: pnpm run lint
        env:
          NODE_OPTIONS: --max-old-space-size=6144

  version:
    name: Determine version
    # 与 test job 相同的触发条件
    if: contains(github.event.pull_request.labels.*.name, 'Build Desktop')
    runs-on: ubuntu-latest
    outputs:
      # 输出版本信息，供后续 job 使用
      version: ${{ steps.set_version.outputs.version }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      # 主要逻辑：确定构建版本号
      - name: Set version
        id: set_version
        run: |
          # 从 apps/desktop/package.json 读取基础版本号
          base_version=$(node -p "require('./apps/desktop/package.json').version")

          # PR 构建：在基础版本号上添加 PR 信息
          pr_number="${{ github.event.pull_request.number }}"
          ci_build_number="${{ github.run_number }}" # CI 构建编号
          version="0.0.0-nightly.pr${pr_number}.${ci_build_number}"
          echo "version=${version}" >> $GITHUB_OUTPUT
          echo "📦 Release Version: ${version} (based on base version ${base_version})"

        env:
          NODE_OPTIONS: --max-old-space-size=6144

      # 输出版本信息总结，方便在 GitHub Actions 界面查看
      - name: Version Summary
        run: |
          echo "🚦 Release Version: ${{ steps.set_version.outputs.version }}"

  build:
    needs: [version, test]
    name: Build Desktop App
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [macos-latest, windows-2025, ubuntu-latest]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9

      # node-linker=hoisted 模式将可以确保 asar 压缩可用
      - name: Install deps
        run: pnpm install --node-linker=hoisted

      - name: Install deps on Desktop
        run: npm run install-isolated --prefix=./apps/desktop

      # 设置 package.json 的版本号
      - name: Set package version
        run: npm run workflow:set-desktop-version ${{ needs.version.outputs.version }} nightly

      # macOS 构建处理
      - name: Build artifact on macOS
        if: runner.os == 'macOS'
        run: npm run desktop:build
        env:
          # 设置更新通道，PR构建为nightly，否则为stable
          UPDATE_CHANNEL: 'nightly'
          APP_URL: http://localhost:3015
          DATABASE_URL: 'postgresql://postgres@localhost:5432/postgres'
          # 默认添加一个加密 SECRET
          KEY_VAULTS_SECRET: 'oLXWIiR/AKF+rWaqy9lHkrYgzpATbW3CtJp3UfkVgpE='
          # macOS 签名和公证配置
          CSC_LINK: ${{ secrets.APPLE_CERTIFICATE_BASE64 }}
          CSC_KEY_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          NEXT_PUBLIC_DESKTOP_PROJECT_ID: ${{ secrets.UMAMI_NIGHTLY_DESKTOP_PROJECT_ID }}
          NEXT_PUBLIC_DESKTOP_UMAMI_BASE_URL: ${{ secrets.UMAMI_NIGHTLY_DESKTOP_BASE_URL }}

          # allow provisionally
          CSC_FOR_PULL_REQUEST: true
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.APPLE_APP_SPECIFIC_PASSWORD }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}

      #  Windows 平台构建处理
      - name: Build artifact on Windows
        if: runner.os == 'Windows'
        run: npm run desktop:build
        env:
          # 设置更新通道，PR构建为nightly，否则为stable
          UPDATE_CHANNEL: 'nightly'
          APP_URL: http://localhost:3015
          DATABASE_URL: 'postgresql://postgres@localhost:5432/postgres'
          KEY_VAULTS_SECRET: 'oLXWIiR/AKF+rWaqy9lHkrYgzpATbW3CtJp3UfkVgpE='
          NEXT_PUBLIC_DESKTOP_PROJECT_ID: ${{ secrets.UMAMI_NIGHTLY_DESKTOP_PROJECT_ID }}
          NEXT_PUBLIC_DESKTOP_UMAMI_BASE_URL: ${{ secrets.UMAMI_NIGHTLY_DESKTOP_BASE_URL }}
          # 将 TEMP 和 TMP 目录设置到 D 盘
          TEMP: D:\temp
          TMP: D:\temp

      # Linux 平台构建处理
      - name: Build artifact on Linux
        if: runner.os == 'Linux'
        run: npm run desktop:build
        env:
          # 设置更新通道，PR构建为nightly，否则为stable
          UPDATE_CHANNEL: 'nightly'
          APP_URL: http://localhost:3015
          DATABASE_URL: 'postgresql://postgres@localhost:5432/postgres'
          KEY_VAULTS_SECRET: 'oLXWIiR/AKF+rWaqy9lHkrYgzpATbW3CtJp3UfkVgpE='
          NEXT_PUBLIC_DESKTOP_PROJECT_ID: ${{ secrets.UMAMI_NIGHTLY_DESKTOP_PROJECT_ID }}
          NEXT_PUBLIC_DESKTOP_UMAMI_BASE_URL: ${{ secrets.UMAMI_NIGHTLY_DESKTOP_BASE_URL }}

      # 上传构建产物
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: release-${{ matrix.os }}
          path: |
            apps/desktop/release/latest*
            apps/desktop/release/*.dmg*
            apps/desktop/release/*.zip*
            apps/desktop/release/*.exe*
            apps/desktop/release/*.AppImage
          retention-days: 5

  publish-pr:
    needs: [build, version]
    name: Publish PR Build
    runs-on: ubuntu-latest
    # Grant write permissions for creating release and commenting on PR
    permissions:
      contents: write
      pull-requests: write
    outputs:
      artifact_path: ${{ steps.set_path.outputs.path }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # 下载所有平台的构建产物
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: release
          pattern: release-*
          merge-multiple: true

      # 列出所有构建产物
      - name: List artifacts
        run: ls -R release

      # 生成PR发布描述
      - name: Generate PR Release Body
        id: pr_release_body
        uses: actions/github-script@v7
        with:
          result-encoding: string
          script: |
            const generateReleaseBody = require('${{ github.workspace }}/.github/scripts/pr-release-body.js');

            const body = generateReleaseBody({
              version: "${{ needs.version.outputs.version }}",
              prNumber: "${{ github.event.pull_request.number }}",
              branch: "${{ github.head_ref }}"
            });

            return body;

      - name: Create Temporary Release for PR
        id: create_release
        uses: softprops/action-gh-release@v1
        with:
          name: PR Build v${{ needs.version.outputs.version }}
          tag_name: v${{ needs.version.outputs.version }}
          #          tag_name: pr-build-${{ github.event.pull_request.number }}-${{ github.sha }}
          body: ${{ steps.pr_release_body.outputs.result }}
          draft: false
          prerelease: true
          files: |
            release/latest*
            release/*.dmg*
            release/*.zip*
            release/*.exe*
            release/*.AppImage
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # 在 PR 上添加评论，包含构建信息和下载链接
      - name: Comment on PR
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const releaseUrl = "${{ steps.create_release.outputs.url }}";
            const prCommentGenerator = require('${{ github.workspace }}/.github/scripts/pr-comment.js');

            const result = await prCommentGenerator({
              github,
              context,
              releaseUrl,
              version: "${{ needs.version.outputs.version }}",
              tag: "v${{ needs.version.outputs.version }}"
            });

            console.log(`评论状态: ${result.updated ? '已更新' : '已创建'}, ID: ${result.id}`);
