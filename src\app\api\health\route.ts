/**
 * Health Check API Endpoint
 * Used by hybrid testing to verify services are ready
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Check database connection
    const dbStatus = await checkDatabase();
    
    // Check AI provider configurations
    const aiStatus = await checkAIProviders();
    
    // Check storage (MinIO/S3)
    const storageStatus = await checkStorage();
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: dbStatus,
        ai_providers: aiStatus,
        storage: storageStatus
      },
      environment: {
        node_env: process.env.NODE_ENV,
        app_url: process.env.APP_URL,
        testing_mode: process.env.ENABLE_MODEL_TESTING === '1'
      }
    };
    
    return NextResponse.json(health, { status: 200 });
    
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function checkDatabase() {
  try {
    // Import database connection
    const { db } = await import('@/database/server/core/db');
    
    // Simple query to check connection
    await db.execute('SELECT 1');
    
    return {
      status: 'healthy',
      type: 'postgresql',
      features: ['pgvector']
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Database connection failed'
    };
  }
}

async function checkAIProviders() {
  const providers = {
    azure_openai: {
      configured: !!(process.env.AZURE_API_KEY && process.env.AZURE_ENDPOINT),
      enabled: process.env.ENABLED_AZURE_OPENAI === '1',
      models: process.env.AZURE_MODEL_LIST?.split(',') || []
    },
    aws_bedrock: {
      configured: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY),
      enabled: process.env.ENABLED_AWS_BEDROCK === '1',
      models: process.env.AWS_BEDROCK_MODEL_LIST?.split(',') || []
    },
    gcp_vertex: {
      configured: !!(process.env.GOOGLE_API_KEY || process.env.GOOGLE_APPLICATION_CREDENTIALS),
      enabled: process.env.ENABLED_GOOGLE === '1',
      models: process.env.GOOGLE_MODEL_LIST?.split(',') || []
    },
    anthropic: {
      configured: !!process.env.ANTHROPIC_API_KEY,
      enabled: process.env.ENABLED_ANTHROPIC === '1',
      models: process.env.ANTHROPIC_MODEL_LIST?.split(',') || []
    },
    openai: {
      configured: !!process.env.OPENAI_API_KEY,
      enabled: process.env.ENABLED_OPENAI === '1',
      models: process.env.OPENAI_MODEL_LIST?.split(',') || []
    }
  };
  
  const configuredCount = Object.values(providers).filter(p => p.configured).length;
  const enabledCount = Object.values(providers).filter(p => p.enabled).length;
  
  return {
    status: configuredCount > 0 ? 'healthy' : 'warning',
    configured_providers: configuredCount,
    enabled_providers: enabledCount,
    providers
  };
}

async function checkStorage() {
  try {
    const s3Endpoint = process.env.S3_ENDPOINT;
    const bucket = process.env.MINIO_LOBE_BUCKET || process.env.S3_BUCKET;
    
    if (!s3Endpoint || !bucket) {
      return {
        status: 'warning',
        message: 'Storage not configured'
      };
    }
    
    return {
      status: 'healthy',
      type: s3Endpoint.includes('localhost') ? 'minio' : 's3',
      endpoint: s3Endpoint,
      bucket
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Storage check failed'
    };
  }
}
